'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'

// User type for authentication
export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  role: 'admin' | 'attorney' | 'paralegal' | 'staff'
  createdAt: Date
}

// Auth state interface
interface AuthState {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
}

// Auth context interface
interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<boolean>
  logout: () => void
  register: (userData: Omit<User, 'id' | 'createdAt'> & { password: string }) => Promise<boolean>
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Mock users for demonstration (in a real app, this would be handled by a backend)
const MOCK_USERS: (User & { password: string })[] = [
  {
    id: '1',
    email: '<EMAIL>',
    password: 'admin123',
    firstName: 'Admin',
    lastName: 'User',
    role: 'admin',
    createdAt: new Date('2024-01-01')
  },
  {
    id: '2',
    email: '<EMAIL>',
    password: 'attorney123',
    firstName: 'John',
    lastName: 'Attorney',
    role: 'attorney',
    createdAt: new Date('2024-01-01')
  }
]

// Storage key for auth data
const AUTH_STORAGE_KEY = 'practice-manager-auth'

// Provider component
interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isLoading: true,
    isAuthenticated: false
  })

  // Load user from localStorage on mount
  useEffect(() => {
    const loadUser = () => {
      try {
        const storedAuth = localStorage.getItem(AUTH_STORAGE_KEY)
        if (storedAuth) {
          const userData = JSON.parse(storedAuth)
          setAuthState({
            user: {
              ...userData,
              createdAt: new Date(userData.createdAt)
            },
            isLoading: false,
            isAuthenticated: true
          })
        } else {
          setAuthState({
            user: null,
            isLoading: false,
            isAuthenticated: false
          })
        }
      } catch (error) {
        console.error('Error loading auth data:', error)
        setAuthState({
          user: null,
          isLoading: false,
          isAuthenticated: false
        })
      }
    }

    loadUser()
  }, [])

  // Login function
  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Find user in mock data
      const user = MOCK_USERS.find(u => u.email === email && u.password === password)
      
      if (user) {
        const userData: User = {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          createdAt: user.createdAt
        }
        
        // Store in localStorage
        localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(userData))
        
        // Update state
        setAuthState({
          user: userData,
          isLoading: false,
          isAuthenticated: true
        })
        
        return true
      }
      
      return false
    } catch (error) {
      console.error('Login error:', error)
      return false
    }
  }

  // Logout function
  const logout = () => {
    localStorage.removeItem(AUTH_STORAGE_KEY)
    setAuthState({
      user: null,
      isLoading: false,
      isAuthenticated: false
    })
  }

  // Register function (placeholder)
  const register = async (userData: Omit<User, 'id' | 'createdAt'> & { password: string }): Promise<boolean> => {
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // In a real app, this would create a new user in the backend
      // For now, we'll just simulate success
      const newUser: User = {
        id: Date.now().toString(),
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        role: userData.role,
        createdAt: new Date()
      }
      
      // Store in localStorage
      localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(newUser))
      
      // Update state
      setAuthState({
        user: newUser,
        isLoading: false,
        isAuthenticated: true
      })
      
      return true
    } catch (error) {
      console.error('Registration error:', error)
      return false
    }
  }

  const value: AuthContextType = {
    ...authState,
    login,
    logout,
    register
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// Hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Higher-order component for protected routes
export function withAuth<P extends object>(Component: React.ComponentType<P>) {
  return function AuthenticatedComponent(props: P) {
    const { isAuthenticated, isLoading } = useAuth()
    
    if (isLoading) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p>Loading...</p>
          </div>
        </div>
      )
    }
    
    if (!isAuthenticated) {
      // In a real app, you might redirect to login page
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">Authentication Required</h2>
            <p>Please log in to access this page.</p>
          </div>
        </div>
      )
    }
    
    return <Component {...props} />
  }
}
