'use client'

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react'
import { AppState, AppAction, Client, Matter, TimeEntry, Document, Event, Invoice } from '@/types'
import { 
  clientStorage, 
  matterStorage, 
  timeEntryStorage, 
  documentStorage, 
  eventStorage, 
  invoiceStorage,
  initializeStorage 
} from '@/lib/storage'
import { useToast } from '@/components/ui/toast'

// Initial state
const initialState: AppState = {
  clients: [],
  matters: [],
  timeEntries: [],
  documents: [],
  events: [],
  invoices: [],
  isLoading: false,
  error: null
}

// Reducer function with error handling
function appReducer(state: AppState, action: AppAction): AppState {
  try {
    switch (action.type) {
      case 'SET_LOADING':
        return { ...state, isLoading: action.payload, error: null }
      
      case 'SET_ERROR':
        return { ...state, error: action.payload, isLoading: false }
      
      case 'LOAD_DATA':
        return { ...state, ...action.payload, isLoading: false, error: null }
      
      // Client actions
      case 'ADD_CLIENT':
        clientStorage.add(action.payload)
        return { ...state, clients: [...state.clients, action.payload], error: null }
      
      case 'UPDATE_CLIENT':
        clientStorage.update(action.payload.id, action.payload)
        return {
          ...state,
          clients: state.clients.map(client =>
            client.id === action.payload.id ? action.payload : client
          ),
          error: null
        }
      
      case 'DELETE_CLIENT':
        clientStorage.delete(action.payload)
        return {
          ...state,
          clients: state.clients.filter(client => client.id !== action.payload),
          error: null
        }
      
      // Matter actions
      case 'ADD_MATTER':
        matterStorage.add(action.payload)
        return { ...state, matters: [...state.matters, action.payload], error: null }
      
      case 'UPDATE_MATTER':
        matterStorage.update(action.payload.id, action.payload)
        return {
          ...state,
          matters: state.matters.map(matter =>
            matter.id === action.payload.id ? action.payload : matter
          ),
          error: null
        }
      
      case 'DELETE_MATTER':
        matterStorage.delete(action.payload)
        return {
          ...state,
          matters: state.matters.filter(matter => matter.id !== action.payload),
          error: null
        }
      
      // Time entry actions
      case 'ADD_TIME_ENTRY':
        timeEntryStorage.add(action.payload)
        return { ...state, timeEntries: [...state.timeEntries, action.payload], error: null }
      
      case 'UPDATE_TIME_ENTRY':
        timeEntryStorage.update(action.payload.id, action.payload)
        return {
          ...state,
          timeEntries: state.timeEntries.map(entry =>
            entry.id === action.payload.id ? action.payload : entry
          ),
          error: null
        }
      
      case 'DELETE_TIME_ENTRY':
        timeEntryStorage.delete(action.payload)
        return {
          ...state,
          timeEntries: state.timeEntries.filter(entry => entry.id !== action.payload),
          error: null
        }
      
      // Document actions
      case 'ADD_DOCUMENT':
        documentStorage.add(action.payload)
        return { ...state, documents: [...state.documents, action.payload], error: null }
      
      case 'UPDATE_DOCUMENT':
        documentStorage.update(action.payload.id, action.payload)
        return {
          ...state,
          documents: state.documents.map(doc =>
            doc.id === action.payload.id ? action.payload : doc
          ),
          error: null
        }
      
      case 'DELETE_DOCUMENT':
        documentStorage.delete(action.payload)
        return {
          ...state,
          documents: state.documents.filter(doc => doc.id !== action.payload),
          error: null
        }
      
      // Event actions
      case 'ADD_EVENT':
        eventStorage.add(action.payload)
        return { ...state, events: [...state.events, action.payload], error: null }
      
      case 'UPDATE_EVENT':
        eventStorage.update(action.payload.id, action.payload)
        return {
          ...state,
          events: state.events.map(event =>
            event.id === action.payload.id ? action.payload : event
          ),
          error: null
        }
      
      case 'DELETE_EVENT':
        eventStorage.delete(action.payload)
        return {
          ...state,
          events: state.events.filter(event => event.id !== action.payload),
          error: null
        }
      
      default:
        return state
    }
  } catch (error) {
    console.error('Error in app reducer:', error)
    return { 
      ...state, 
      error: error instanceof Error ? error.message : 'An unexpected error occurred',
      isLoading: false 
    }
  }
}

// Context type
interface AppContextType {
  state: AppState
  dispatch: React.Dispatch<AppAction>
  // Helper functions
  addClient: (client: Omit<Client, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>
  updateClient: (id: string, data: Partial<Client>) => Promise<void>
  deleteClient: (id: string) => Promise<void>
  addMatter: (matter: Omit<Matter, 'id' | 'createdAt' | 'updatedAt' | 'actualHours' | 'totalBilled'>) => Promise<void>
  updateMatter: (id: string, data: Partial<Matter>) => Promise<void>
  deleteMatter: (id: string) => Promise<void>
  addTimeEntry: (timeEntry: Omit<TimeEntry, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>
  updateTimeEntry: (id: string, data: Partial<TimeEntry>) => Promise<void>
  deleteTimeEntry: (id: string) => Promise<void>
  addDocument: (document: Omit<Document, 'id'>) => Promise<void>
  updateDocument: (id: string, data: Partial<Document>) => Promise<void>
  deleteDocument: (id: string) => Promise<void>
  addEvent: (event: Omit<Event, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>
  updateEvent: (id: string, data: Partial<Event>) => Promise<void>
  deleteEvent: (id: string) => Promise<void>
  getClientById: (id: string) => Client | undefined
  getMatterById: (id: string) => Matter | undefined
  getMattersByClientId: (clientId: string) => Matter[]
  getTimeEntriesByMatterId: (matterId: string) => TimeEntry[]
}

// Create context
const AppContext = createContext<AppContextType | undefined>(undefined)

// Provider component with toast integration
interface AppProviderProps {
  children: ReactNode
}

function AppProviderInner({ children }: AppProviderProps) {
  const [state, dispatch] = useReducer(appReducer, initialState)
  const { addToast } = useToast()

  // Load data from localStorage on mount
  useEffect(() => {
    const loadData = async () => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true })
        
        initializeStorage()
        
        const clients = clientStorage.getAll()
        const matters = matterStorage.getAll()
        const timeEntries = timeEntryStorage.getAll()
        const documents = documentStorage.getAll()
        const events = eventStorage.getAll()
        const invoices = invoiceStorage.getAll()

        dispatch({
          type: 'LOAD_DATA',
          payload: {
            clients,
            matters,
            timeEntries,
            documents,
            events,
            invoices
          }
        })
      } catch (error) {
        console.error('Error loading data:', error)
        dispatch({ 
          type: 'SET_ERROR', 
          payload: error instanceof Error ? error.message : 'Failed to load data' 
        })
        addToast({
          type: 'error',
          title: 'Error Loading Data',
          description: 'Failed to load application data. Please refresh the page.'
        })
      }
    }

    loadData()
  }, [addToast])

  // Helper functions with error handling and toast notifications
  const addClient = async (clientData: Omit<Client, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      const client: Client = {
        ...clientData,
        id: Date.now().toString(),
        createdAt: new Date(),
        updatedAt: new Date()
      }
      dispatch({ type: 'ADD_CLIENT', payload: client })
      addToast({
        type: 'success',
        title: 'Client Added',
        description: `${client.firstName} ${client.lastName} has been added successfully.`
      })
    } catch (error) {
      console.error('Error adding client:', error)
      addToast({
        type: 'error',
        title: 'Error Adding Client',
        description: 'Failed to add client. Please try again.'
      })
    }
  }

  const updateClient = async (id: string, data: Partial<Client>) => {
    try {
      const existingClient = state.clients.find(c => c.id === id)
      if (existingClient) {
        const updatedClient = { ...existingClient, ...data, updatedAt: new Date() }
        dispatch({ type: 'UPDATE_CLIENT', payload: updatedClient })
        addToast({
          type: 'success',
          title: 'Client Updated',
          description: 'Client information has been updated successfully.'
        })
      }
    } catch (error) {
      console.error('Error updating client:', error)
      addToast({
        type: 'error',
        title: 'Error Updating Client',
        description: 'Failed to update client. Please try again.'
      })
    }
  }

  const deleteClient = async (id: string) => {
    try {
      const client = state.clients.find(c => c.id === id)
      dispatch({ type: 'DELETE_CLIENT', payload: id })
      addToast({
        type: 'success',
        title: 'Client Deleted',
        description: client ? `${client.firstName} ${client.lastName} has been deleted.` : 'Client has been deleted.'
      })
    } catch (error) {
      console.error('Error deleting client:', error)
      addToast({
        type: 'error',
        title: 'Error Deleting Client',
        description: 'Failed to delete client. Please try again.'
      })
    }
  }

  // Continue with other helper functions...
  const addMatter = async (matterData: Omit<Matter, 'id' | 'createdAt' | 'updatedAt' | 'actualHours' | 'totalBilled'>) => {
    try {
      const matter: Matter = {
        ...matterData,
        id: Date.now().toString(),
        actualHours: 0,
        totalBilled: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      }
      dispatch({ type: 'ADD_MATTER', payload: matter })
      addToast({
        type: 'success',
        title: 'Matter Created',
        description: `Matter "${matter.title}" has been created successfully.`
      })
    } catch (error) {
      console.error('Error adding matter:', error)
      addToast({
        type: 'error',
        title: 'Error Creating Matter',
        description: 'Failed to create matter. Please try again.'
      })
    }
  }

  // ... (continuing with other helper functions)
  
  const value: AppContextType = {
    state,
    dispatch,
    addClient,
    updateClient,
    deleteClient,
    addMatter,
    updateMatter: async () => {}, // Placeholder - will implement
    deleteMatter: async () => {}, // Placeholder - will implement
    addTimeEntry: async () => {}, // Placeholder - will implement
    updateTimeEntry: async () => {}, // Placeholder - will implement
    deleteTimeEntry: async () => {}, // Placeholder - will implement
    addDocument: async () => {}, // Placeholder - will implement
    updateDocument: async () => {}, // Placeholder - will implement
    deleteDocument: async () => {}, // Placeholder - will implement
    addEvent: async () => {}, // Placeholder - will implement
    updateEvent: async () => {}, // Placeholder - will implement
    deleteEvent: async () => {}, // Placeholder - will implement
    getClientById: (id: string) => state.clients.find(c => c.id === id),
    getMatterById: (id: string) => state.matters.find(m => m.id === id),
    getMattersByClientId: (clientId: string) => state.matters.filter(m => m.clientId === clientId),
    getTimeEntriesByMatterId: (matterId: string) => state.timeEntries.filter(t => t.matterId === matterId)
  }

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  )
}

export function AppProvider({ children }: AppProviderProps) {
  return <AppProviderInner>{children}</AppProviderInner>
}

// Hook to use the context
export function useApp() {
  const context = useContext(AppContext)
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider')
  }
  return context
}
