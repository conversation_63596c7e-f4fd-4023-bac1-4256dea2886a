'use client'

import { useState } from 'react'
import { MainLayout } from '@/components/layout/MainLayout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { useApp } from '@/contexts/AppContext'
import { Plus, Edit, Trash2 } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { Timer } from '@/components/ui/Timer'
import { TimeEntryForm } from '@/components/forms/TimeEntryForm'
import { TimeEntry } from '@/types'

export default function TimePage() {
  const { state, deleteTimeEntry } = useApp()
  const { timeEntries, matters, clients } = state
  
  const [selectedTimeEntry, setSelectedTimeEntry] = useState<TimeEntry | null>(null)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)

  // Recent time entries (last 10)
  const recentTimeEntries = timeEntries
    .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
    .slice(0, 10)

  // Calculate totals
  const totalHours = timeEntries.reduce((sum, entry) => sum + entry.duration / 60, 0)
  const billableHours = timeEntries
    .filter(entry => entry.isBillable)
    .reduce((sum, entry) => sum + entry.duration / 60, 0)
  const unbilledHours = timeEntries
    .filter(entry => entry.isBillable && !entry.isInvoiced)
    .reduce((sum, entry) => sum + entry.duration / 60, 0)
  const totalValue = timeEntries
    .filter(entry => entry.isBillable)
    .reduce((sum, entry) => sum + (entry.duration / 60) * entry.billableRate, 0)

  const handleEditTimeEntry = (timeEntry: TimeEntry) => {
    setSelectedTimeEntry(timeEntry)
    setIsEditDialogOpen(true)
  }

  const handleDeleteTimeEntry = (timeEntryId: string) => {
    if (confirm('Are you sure you want to delete this time entry? This action cannot be undone.')) {
      deleteTimeEntry(timeEntryId)
    }
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Time Tracking</h1>
            <p className="text-muted-foreground">
              Track billable hours and manage time entries
            </p>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Add Time Entry
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Add Time Entry</DialogTitle>
                <DialogDescription>
                  Manually add a time entry for work performed.
                </DialogDescription>
              </DialogHeader>
              <TimeEntryForm onSuccess={() => setIsAddDialogOpen(false)} />
            </DialogContent>
          </Dialog>
        </div>

        {/* Timer Section */}
        <Timer />

        {/* Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Hours</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalHours.toFixed(1)}</div>
              <p className="text-xs text-muted-foreground">All time entries</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Billable Hours</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{billableHours.toFixed(1)}</div>
              <p className="text-xs text-muted-foreground">Ready to invoice</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Unbilled Hours</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{unbilledHours.toFixed(1)}</div>
              <p className="text-xs text-muted-foreground">Pending invoice</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${totalValue.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">Billable time value</p>
            </CardContent>
          </Card>
        </div>

        {/* Recent Time Entries */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Time Entries</CardTitle>
            <CardDescription>
              Your latest time tracking activity
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Description</TableHead>
                    <TableHead>Client/Matter</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead>Rate</TableHead>
                    <TableHead>Value</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {recentTimeEntries.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8">
                        <div className="text-muted-foreground">
                          No time entries yet. Start tracking your time to see them here.
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    recentTimeEntries.map((entry) => {
                      const matter = matters.find(m => m.id === entry.matterId)
                      const client = clients.find(c => c.id === entry.clientId)
                      const value = (entry.duration / 60) * entry.billableRate
                      
                      return (
                        <TableRow key={entry.id}>
                          <TableCell>
                            <div className="font-medium">{entry.description}</div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">
                                {client ? `${client.firstName} ${client.lastName}` : 'Unknown Client'}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {matter?.title || 'Unknown Matter'}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="font-mono">
                            {(entry.duration / 60).toFixed(1)}h
                          </TableCell>
                          <TableCell className="font-mono">
                            ${entry.billableRate}/hr
                          </TableCell>
                          <TableCell className="font-mono">
                            ${value.toFixed(2)}
                          </TableCell>
                          <TableCell>
                            <div className="flex gap-1">
                              <Badge 
                                variant={entry.isBillable ? "default" : "secondary"} 
                                className="text-xs"
                              >
                                {entry.isBillable ? "Billable" : "Non-billable"}
                              </Badge>
                              {entry.isInvoiced && (
                                <Badge variant="outline" className="text-xs">
                                  Invoiced
                                </Badge>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="text-sm text-muted-foreground">
                            {formatDistanceToNow(entry.createdAt, { addSuffix: true })}
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEditTimeEntry(entry)}
                              >
                                <Edit className="w-4 h-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteTimeEntry(entry.id)}
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    })
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
                </Card>

        {/* Edit Time Entry Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Edit Time Entry</DialogTitle>
              <DialogDescription>
                Update time entry details and billing information.
              </DialogDescription>
            </DialogHeader>
            {selectedTimeEntry && (
              <TimeEntryForm 
                timeEntry={selectedTimeEntry}
                onSuccess={() => {
                  setIsEditDialogOpen(false)
                  setSelectedTimeEntry(null)
                }}
              />
            )}
          </DialogContent>
        </Dialog>
    </div>
    </MainLayout>
  )
} 