'use client'

import { useState } from 'react'
import { MainLayout } from '@/components/layout/MainLayout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useApp } from '@/contexts/AppContext'
import { Event as EventType } from '@/types'
import { Plus, Calendar, Clock, MapPin, User, Edit, Trash2, ChevronLeft, ChevronRight, Grid3X3, List, CalendarDays } from 'lucide-react'
import {
  format,
  startOfMonth,
  endOfMonth,
  eachDayOfInterval,
  isSameDay,
  isToday,
  isSameMonth,
  addMonths,
  subMonths,
  startOfWeek,
  endOfWeek,
  addWeeks,
  subWeeks,
  addDays,
  subDays,
  startOfDay,
  endOfDay,
  isWithinInterval
} from 'date-fns'
import { EventForm } from '@/components/forms/EventForm'

type ViewMode = 'month' | 'week' | 'day'

export default function CalendarPage() {
  const { state, deleteEvent } = useApp()
  const { events, matters, clients } = state

  const [selectedEvent, setSelectedEvent] = useState<EventType | null>(null)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [currentDate, setCurrentDate] = useState(new Date())
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [viewMode, setViewMode] = useState<ViewMode>('month')

  // Calendar helpers
  const monthStart = startOfMonth(currentDate)
  const monthEnd = endOfMonth(currentDate)
  const calendarDays = eachDayOfInterval({ start: monthStart, end: monthEnd })

  // Get events for a specific day
  const getEventsForDay = (date: Date) => {
    return events.filter(event => isSameDay(event.startDate, date))
  }

  // Get upcoming events (next 7 days)
  const upcomingEvents = events
    .filter(event => event.startDate >= new Date())
    .sort((a, b) => a.startDate.getTime() - b.startDate.getTime())
    .slice(0, 10)

  const handleEditEvent = (event: EventType) => {
    setSelectedEvent(event)
    setIsEditDialogOpen(true)
  }

  const handleDeleteEvent = (eventId: string) => {
    if (confirm('Are you sure you want to delete this event? This action cannot be undone.')) {
      deleteEvent(eventId)
    }
  }

  // Navigation functions
  const handlePrevious = () => {
    switch (viewMode) {
      case 'month':
        setCurrentDate(subMonths(currentDate, 1))
        break
      case 'week':
        setCurrentDate(subWeeks(currentDate, 1))
        break
      case 'day':
        setCurrentDate(subDays(currentDate, 1))
        break
    }
  }

  const handleNext = () => {
    switch (viewMode) {
      case 'month':
        setCurrentDate(addMonths(currentDate, 1))
        break
      case 'week':
        setCurrentDate(addWeeks(currentDate, 1))
        break
      case 'day':
        setCurrentDate(addDays(currentDate, 1))
        break
    }
  }

  const handleToday = () => {
    setCurrentDate(new Date())
  }

  // Get date range based on view mode
  const getDateRange = () => {
    switch (viewMode) {
      case 'month':
        return { start: monthStart, end: monthEnd }
      case 'week':
        return { start: startOfWeek(currentDate), end: endOfWeek(currentDate) }
      case 'day':
        return { start: startOfDay(currentDate), end: endOfDay(currentDate) }
    }
  }

  // Get formatted title based on view mode
  const getViewTitle = () => {
    switch (viewMode) {
      case 'month':
        return format(currentDate, 'MMMM yyyy')
      case 'week':
        const weekStart = startOfWeek(currentDate)
        const weekEnd = endOfWeek(currentDate)
        return `${format(weekStart, 'MMM d')} - ${format(weekEnd, 'MMM d, yyyy')}`
      case 'day':
        return format(currentDate, 'EEEE, MMMM d, yyyy')
    }
  }

  const getEventTypeColor = (type: string) => {
    switch (type) {
      case 'court-date':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'deadline':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'meeting':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'appointment':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'reminder':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getEventTypeIcon = (type: string) => {
    switch (type) {
      case 'court-date':
        return '⚖️'
      case 'deadline':
        return '⏰'
      case 'meeting':
        return '🤝'
      case 'appointment':
        return '📅'
      case 'reminder':
        return '🔔'
      default:
        return '📅'
    }
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Calendar</h1>
            <p className="text-muted-foreground">
              Manage appointments, court dates, and deadlines
            </p>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                New Event
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create New Event</DialogTitle>
                <DialogDescription>
                  Schedule a new appointment, court date, or deadline.
                </DialogDescription>
              </DialogHeader>
              <EventForm onSuccess={() => setIsAddDialogOpen(false)} />
            </DialogContent>
          </Dialog>
        </div>

        {/* View Mode Tabs */}
        <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as ViewMode)} className="w-full">
          <div className="flex items-center justify-between mb-6">
            <TabsList className="grid w-full max-w-md grid-cols-3">
              <TabsTrigger value="month" className="flex items-center gap-2">
                <Grid3X3 className="w-4 h-4" />
                Month
              </TabsTrigger>
              <TabsTrigger value="week" className="flex items-center gap-2">
                <CalendarDays className="w-4 h-4" />
                Week
              </TabsTrigger>
              <TabsTrigger value="day" className="flex items-center gap-2">
                <List className="w-4 h-4" />
                Day
              </TabsTrigger>
            </TabsList>

            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handleToday}>
                Today
              </Button>
              <Button variant="outline" size="sm" onClick={handlePrevious}>
                <ChevronLeft className="w-4 h-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={handleNext}>
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          </div>

          <div className="grid gap-6 lg:grid-cols-3">
            {/* Calendar */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Calendar className="w-5 h-5 mr-2" />
                    {getViewTitle()}
                  </CardTitle>
                </CardHeader>
              <CardContent>
                {/* Month View */}
                <TabsContent value="month" className="mt-0">
                  <div className="grid grid-cols-7 gap-1">
                    {/* Day headers */}
                    {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                      <div key={day} className="p-2 text-center text-sm font-medium text-muted-foreground">
                        {day}
                      </div>
                    ))}

                    {/* Calendar days */}
                    {calendarDays.map(day => {
                      const dayEvents = getEventsForDay(day)
                      const isSelected = selectedDate && isSameDay(day, selectedDate)
                      const isCurrentMonth = isSameMonth(day, currentDate)

                      return (
                        <div
                          key={day.toISOString()}
                          className={`
                            min-h-[100px] p-1 border cursor-pointer hover:bg-muted/50 transition-colors
                            ${isToday(day) ? 'bg-blue-50 border-blue-200' : ''}
                            ${isSelected ? 'bg-blue-100 border-blue-300' : ''}
                            ${!isCurrentMonth ? 'text-muted-foreground bg-muted/20' : ''}
                          `}
                          onClick={() => setSelectedDate(day)}
                        >
                          <div className={`text-sm mb-1 ${isToday(day) ? 'font-bold text-blue-700' : ''}`}>
                            {format(day, 'd')}
                          </div>
                          <div className="space-y-1">
                            {dayEvents.slice(0, 2).map(event => (
                              <div
                                key={event.id}
                                className={`text-xs p-1 rounded truncate ${getEventTypeColor(event.type)}`}
                                title={event.title}
                              >
                                {getEventTypeIcon(event.type)} {event.title}
                              </div>
                            ))}
                            {dayEvents.length > 2 && (
                              <div className="text-xs text-muted-foreground">
                                +{dayEvents.length - 2} more
                              </div>
                            )}
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </TabsContent>

                {/* Week View */}
                <TabsContent value="week" className="mt-0">
                  <WeekView
                    currentDate={currentDate}
                    events={events}
                    getEventsForDay={getEventsForDay}
                    getEventTypeColor={getEventTypeColor}
                    getEventTypeIcon={getEventTypeIcon}
                    onEditEvent={handleEditEvent}
                    onDeleteEvent={handleDeleteEvent}
                  />
                </TabsContent>

                {/* Day View */}
                <TabsContent value="day" className="mt-0">
                  <DayView
                    currentDate={currentDate}
                    events={getEventsForDay(currentDate)}
                    getEventTypeColor={getEventTypeColor}
                    getEventTypeIcon={getEventTypeIcon}
                    onEditEvent={handleEditEvent}
                    onDeleteEvent={handleDeleteEvent}
                    matters={matters}
                    clients={clients}
                  />
                </TabsContent>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Stats */}
            <div className="grid gap-4 grid-cols-2 lg:grid-cols-1">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Total Events</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{events.length}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">This Month</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {events.filter(e => isSameMonth(e.startDate, currentDate)).length}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Upcoming Events */}
            <Card>
              <CardHeader>
                <CardTitle>Upcoming Events</CardTitle>
                <CardDescription>Next 10 events</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {upcomingEvents.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      No upcoming events
                    </div>
                  ) : (
                    upcomingEvents.map(event => {
                      const matter = event.matterId ? matters.find(m => m.id === event.matterId) : null
                      const client = matter ? clients.find(c => c.id === matter.clientId) : 
                                    event.clientId ? clients.find(c => c.id === event.clientId) : null
                      
                      return (
                        <div key={event.id} className="border rounded-lg p-3 space-y-2">
                          <div className="flex items-start justify-between">
                            <div className="flex-1 min-w-0">
                              <div className="font-medium truncate">{event.title}</div>
                              <div className="text-sm text-muted-foreground">
                                {format(event.startDate, 'MMM d, yyyy')}
                                {!event.allDay && (
                                  <span> at {format(event.startDate, 'h:mm a')}</span>
                                )}
                              </div>
                              {client && (
                                <div className="text-xs text-muted-foreground flex items-center mt-1">
                                  <User className="w-3 h-3 mr-1" />
                                  {client.firstName} {client.lastName}
                                </div>
                              )}
                              {matter && (
                                <div className="text-xs text-muted-foreground">
                                  Matter: {matter.title}
                                </div>
                              )}
                              {event.location && (
                                <div className="text-xs text-muted-foreground flex items-center">
                                  <MapPin className="w-3 h-3 mr-1" />
                                  {event.location}
                                </div>
                              )}
                            </div>
                            <div className="flex gap-1 ml-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEditEvent(event)}
                              >
                                <Edit className="w-3 h-3" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteEvent(event.id)}
                              >
                                <Trash2 className="w-3 h-3" />
                              </Button>
                            </div>
                          </div>
                          <Badge variant="outline" className={getEventTypeColor(event.type)}>
                            {getEventTypeIcon(event.type)} {event.type.replace('-', ' ')}
                          </Badge>
                        </div>
                      )
                    })
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Edit Event Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Edit Event</DialogTitle>
              <DialogDescription>
                Update event details and scheduling information.
              </DialogDescription>
            </DialogHeader>
            {selectedEvent && (
              <EventForm 
                event={selectedEvent}
                onSuccess={() => {
                  setIsEditDialogOpen(false)
                  setSelectedEvent(null)
                }}
              />
            )}
          </DialogContent>
        </Dialog>
        </Tabs>
      </div>
    </MainLayout>
  )
}

// Week View Component
interface WeekViewProps {
  currentDate: Date
  events: EventType[]
  getEventsForDay: (date: Date) => EventType[]
  getEventTypeColor: (type: string) => string
  getEventTypeIcon: (type: string) => string
  onEditEvent: (event: EventType) => void
  onDeleteEvent: (eventId: string) => void
}

function WeekView({
  currentDate,
  events,
  getEventsForDay,
  getEventTypeColor,
  getEventTypeIcon,
  onEditEvent,
  onDeleteEvent
}: WeekViewProps) {
  const weekStart = startOfWeek(currentDate)
  const weekDays = eachDayOfInterval({ start: weekStart, end: endOfWeek(currentDate) })

  return (
    <div className="grid grid-cols-7 gap-1">
      {/* Day headers */}
      {weekDays.map(day => (
        <div key={day.toISOString()} className="p-2 text-center border-b">
          <div className="text-sm font-medium text-muted-foreground">
            {format(day, 'EEE')}
          </div>
          <div className={`text-lg font-semibold ${isToday(day) ? 'text-blue-600' : ''}`}>
            {format(day, 'd')}
          </div>
        </div>
      ))}

      {/* Week days with events */}
      {weekDays.map(day => {
        const dayEvents = getEventsForDay(day)

        return (
          <div key={day.toISOString()} className="min-h-[300px] p-2 border-r">
            <div className="space-y-1">
              {dayEvents.map(event => (
                <div
                  key={event.id}
                  className={`text-xs p-2 rounded cursor-pointer hover:opacity-80 ${getEventTypeColor(event.type)}`}
                  onClick={() => onEditEvent(event)}
                >
                  <div className="font-medium truncate">
                    {getEventTypeIcon(event.type)} {event.title}
                  </div>
                  {!event.allDay && (
                    <div className="text-xs opacity-75">
                      {format(event.startDate, 'h:mm a')}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )
      })}
    </div>
  )
}

// Day View Component
interface DayViewProps {
  currentDate: Date
  events: EventType[]
  getEventTypeColor: (type: string) => string
  getEventTypeIcon: (type: string) => string
  onEditEvent: (event: EventType) => void
  onDeleteEvent: (eventId: string) => void
  matters: any[]
  clients: any[]
}

function DayView({
  currentDate,
  events,
  getEventTypeColor,
  getEventTypeIcon,
  onEditEvent,
  onDeleteEvent,
  matters,
  clients
}: DayViewProps) {
  const sortedEvents = events.sort((a, b) => a.startDate.getTime() - b.startDate.getTime())

  return (
    <div className="space-y-4">
      <div className="text-center py-4 border-b">
        <div className="text-2xl font-bold">{format(currentDate, 'd')}</div>
        <div className="text-muted-foreground">{format(currentDate, 'EEEE, MMMM yyyy')}</div>
      </div>

      {sortedEvents.length === 0 ? (
        <div className="text-center py-12 text-muted-foreground">
          No events scheduled for this day
        </div>
      ) : (
        <div className="space-y-3">
          {sortedEvents.map(event => {
            const matter = event.matterId ? matters.find(m => m.id === event.matterId) : null
            const client = matter ? clients.find(c => c.id === matter.clientId) :
                          event.clientId ? clients.find(c => c.id === event.clientId) : null

            return (
              <div key={event.id} className="border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="outline" className={getEventTypeColor(event.type)}>
                        {getEventTypeIcon(event.type)} {event.type.replace('-', ' ')}
                      </Badge>
                      {!event.allDay && (
                        <span className="text-sm text-muted-foreground">
                          {format(event.startDate, 'h:mm a')}
                          {event.endDate && ` - ${format(event.endDate, 'h:mm a')}`}
                        </span>
                      )}
                    </div>

                    <h3 className="font-semibold text-lg mb-1">{event.title}</h3>

                    {event.description && (
                      <p className="text-muted-foreground mb-2">{event.description}</p>
                    )}

                    <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
                      {client && (
                        <div className="flex items-center">
                          <User className="w-4 h-4 mr-1" />
                          {client.firstName} {client.lastName}
                        </div>
                      )}
                      {matter && (
                        <div>Matter: {matter.title}</div>
                      )}
                      {event.location && (
                        <div className="flex items-center">
                          <MapPin className="w-4 h-4 mr-1" />
                          {event.location}
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex gap-1 ml-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEditEvent(event)}
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onDeleteEvent(event.id)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      )}
    </div>
  )
}