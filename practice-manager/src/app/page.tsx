'use client'

import { MainLayout } from '@/components/layout/MainLayout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useApp } from '@/contexts/AppContext'
import { LoadingSpinner, LoadingCard } from '@/components/ui/loading-spinner'
import { SimpleBarChart, SimpleDonutChart } from '@/components/ui/simple-chart'
import { Users, Briefcase, Clock, DollarSign, Plus, ArrowRight, AlertCircle } from 'lucide-react'
import Link from 'next/link'
import { formatDistanceToNow, format, subMonths, startOfMonth, endOfMonth } from 'date-fns'

export default function DashboardPage() {
  const { state } = useApp()
  const { clients, matters, timeEntries, isLoading, error } = state

  // Show error state
  if (error) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="max-w-md">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-destructive">
                <AlertCircle className="w-5 h-5" />
                Error Loading Dashboard
              </CardTitle>
              <CardDescription>
                {error}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={() => window.location.reload()} className="w-full">
                Refresh Page
              </Button>
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    )
  }

  // Calculate dashboard statistics
  const totalClients = clients.length
  const activeClients = clients.filter(c => c.status === 'active').length
  const activeMatters = matters.filter(m => m.status === 'open').length
  const totalMatters = matters.length

  // Calculate comprehensive analytics
  const totalRevenue = timeEntries
    .filter(t => t.isInvoiced)
    .reduce((sum, t) => sum + (t.duration / 60) * t.billableRate, 0)

  const unbilledHours = timeEntries
    .filter(t => t.isBillable && !t.isInvoiced)
    .reduce((sum, t) => sum + t.duration / 60, 0)

  const unbilledValue = timeEntries
    .filter(t => t.isBillable && !t.isInvoiced)
    .reduce((sum, t) => sum + (t.duration / 60) * t.billableRate, 0)

  // Additional analytics
  const totalHoursThisMonth = timeEntries
    .filter(t => {
      const entryDate = new Date(t.createdAt)
      const now = new Date()
      return entryDate.getMonth() === now.getMonth() && entryDate.getFullYear() === now.getFullYear()
    })
    .reduce((sum, t) => sum + t.duration / 60, 0)

  const revenueThisMonth = timeEntries
    .filter(t => {
      const entryDate = new Date(t.createdAt)
      const now = new Date()
      return t.isInvoiced && entryDate.getMonth() === now.getMonth() && entryDate.getFullYear() === now.getFullYear()
    })
    .reduce((sum, t) => sum + (t.duration / 60) * t.billableRate, 0)

  const averageHourlyRate = timeEntries.length > 0
    ? timeEntries.reduce((sum, t) => sum + t.billableRate, 0) / timeEntries.length
    : 0

  const completedMatters = matters.filter(m => m.status === 'closed').length
  const overdueMatters = matters.filter(m => {
    if (!m.deadline) return false
    return new Date(m.deadline) < new Date() && m.status !== 'closed'
  }).length

  // Chart data calculations
  const last6MonthsRevenue = Array.from({ length: 6 }, (_, i) => {
    const date = subMonths(new Date(), 5 - i)
    const monthStart = startOfMonth(date)
    const monthEnd = endOfMonth(date)

    const monthRevenue = timeEntries
      .filter(t => {
        const entryDate = new Date(t.createdAt)
        return t.isInvoiced && entryDate >= monthStart && entryDate <= monthEnd
      })
      .reduce((sum, t) => sum + (t.duration / 60) * t.billableRate, 0)

    return {
      label: format(date, 'MMM'),
      value: monthRevenue
    }
  })

  const matterTypeDistribution = matters.reduce((acc, matter) => {
    const type = matter.matterType || 'Other'
    acc[type] = (acc[type] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const matterTypeChartData = Object.entries(matterTypeDistribution).map(([type, count]) => ({
    label: type,
    value: count
  }))

  // Recent activity
  const recentTimeEntries = timeEntries
    .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
    .slice(0, 5)

  const recentMatters = matters
    .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
    .slice(0, 5)

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Dashboard</h1>
            <p className="text-muted-foreground">
              Welcome to your practice management overview
            </p>
          </div>
          <div className="flex gap-2">
            <Button asChild>
              <Link href="/clients/new">
                <Plus className="w-4 h-4 mr-2" />
                New Client
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/matters/new">
                <Plus className="w-4 h-4 mr-2" />
                New Matter
              </Link>
            </Button>
          </div>
        </div>

        {/* Primary Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <LoadingCard isLoading={isLoading}>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Clients</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{totalClients}</div>
                <p className="text-xs text-muted-foreground">
                  {activeClients} active clients
                </p>
              </CardContent>
            </Card>
          </LoadingCard>

          <LoadingCard isLoading={isLoading}>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Matters</CardTitle>
                <Briefcase className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{activeMatters}</div>
                <p className="text-xs text-muted-foreground">
                  {overdueMatters > 0 && (
                    <span className="text-red-600">{overdueMatters} overdue • </span>
                  )}
                  {completedMatters} completed
                </p>
              </CardContent>
            </Card>
          </LoadingCard>

          <LoadingCard isLoading={isLoading}>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Unbilled Hours</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{unbilledHours.toFixed(1)}</div>
                <p className="text-xs text-muted-foreground">
                  ${unbilledValue.toLocaleString()} value
                </p>
              </CardContent>
            </Card>
          </LoadingCard>

          <LoadingCard isLoading={isLoading}>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">${totalRevenue.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  ${revenueThisMonth.toLocaleString()} this month
                </p>
              </CardContent>
            </Card>
          </LoadingCard>
        </div>

        {/* Secondary Analytics Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <LoadingCard isLoading={isLoading}>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Hours This Month</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{totalHoursThisMonth.toFixed(1)}</div>
                <p className="text-xs text-muted-foreground">
                  Billable & non-billable
                </p>
              </CardContent>
            </Card>
          </LoadingCard>

          <LoadingCard isLoading={isLoading}>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg. Hourly Rate</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">${averageHourlyRate.toFixed(0)}</div>
                <p className="text-xs text-muted-foreground">
                  Across all time entries
                </p>
              </CardContent>
            </Card>
          </LoadingCard>

          <LoadingCard isLoading={isLoading}>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Matter Types</CardTitle>
                <Briefcase className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {new Set(matters.map(m => m.matterType)).size}
                </div>
                <p className="text-xs text-muted-foreground">
                  Different practice areas
                </p>
              </CardContent>
            </Card>
          </LoadingCard>

          <LoadingCard isLoading={isLoading}>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Collection Rate</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {totalRevenue + unbilledValue > 0
                    ? Math.round((totalRevenue / (totalRevenue + unbilledValue)) * 100)
                    : 0}%
                </div>
                <p className="text-xs text-muted-foreground">
                  Revenue vs. total billed
                </p>
              </CardContent>
            </Card>
          </LoadingCard>
        </div>

        {/* Analytics Charts */}
        <div className="grid gap-6 lg:grid-cols-2">
          <LoadingCard isLoading={isLoading}>
            <Card>
              <CardHeader>
                <CardTitle>Revenue Trend</CardTitle>
                <CardDescription>Monthly revenue over the last 6 months</CardDescription>
              </CardHeader>
              <CardContent>
                <SimpleBarChart
                  data={last6MonthsRevenue}
                  height={250}
                  className="w-full"
                />
              </CardContent>
            </Card>
          </LoadingCard>

          <LoadingCard isLoading={isLoading}>
            <Card>
              <CardHeader>
                <CardTitle>Matter Distribution</CardTitle>
                <CardDescription>Cases by practice area</CardDescription>
              </CardHeader>
              <CardContent>
                <SimpleDonutChart
                  data={matterTypeChartData}
                  size={200}
                  className="w-full"
                />
              </CardContent>
            </Card>
          </LoadingCard>
        </div>

        {/* Analytics Charts */}
        <div className="grid gap-6 lg:grid-cols-2">
          <LoadingCard isLoading={isLoading}>
            <Card>
              <CardHeader>
                <CardTitle>Revenue Trend</CardTitle>
                <CardDescription>Monthly revenue over the last 6 months</CardDescription>
              </CardHeader>
              <CardContent>
                <SimpleBarChart
                  data={last6MonthsRevenue}
                  height={250}
                  className="w-full"
                />
              </CardContent>
            </Card>
          </LoadingCard>

          <LoadingCard isLoading={isLoading}>
            <Card>
              <CardHeader>
                <CardTitle>Matter Distribution</CardTitle>
                <CardDescription>Cases by practice area</CardDescription>
              </CardHeader>
              <CardContent>
                <SimpleDonutChart
                  data={matterTypeChartData}
                  size={200}
                  className="w-full"
                />
              </CardContent>
            </Card>
          </LoadingCard>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks to help you get things done quickly
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Button variant="outline" className="h-auto flex-col p-4" asChild>
                <Link href="/time/new">
                  <Clock className="w-6 h-6 mb-2" />
                  <span>Start Timer</span>
                </Link>
              </Button>
              <Button variant="outline" className="h-auto flex-col p-4" asChild>
                <Link href="/clients">
                  <Users className="w-6 h-6 mb-2" />
                  <span>View Clients</span>
                </Link>
              </Button>
              <Button variant="outline" className="h-auto flex-col p-4" asChild>
                <Link href="/matters">
                  <Briefcase className="w-6 h-6 mb-2" />
                  <span>Manage Matters</span>
                </Link>
              </Button>
              <Button variant="outline" className="h-auto flex-col p-4" asChild>
                <Link href="/billing">
                  <DollarSign className="w-6 h-6 mb-2" />
                  <span>Create Invoice</span>
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Recent Time Entries */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Recent Time Entries</CardTitle>
                <CardDescription>Latest time tracking activity</CardDescription>
              </div>
              <Button variant="ghost" size="sm" asChild>
                <Link href="/time">
                  View All <ArrowRight className="w-4 h-4 ml-1" />
                </Link>
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentTimeEntries.length === 0 ? (
                  <p className="text-sm text-muted-foreground">No time entries yet</p>
                ) : (
                  recentTimeEntries.map((entry) => {
                    const matter = matters.find(m => m.id === entry.matterId)
                    const client = clients.find(c => c.id === entry.clientId)
                    
                    return (
                      <div key={entry.id} className="flex items-center justify-between">
                        <div className="space-y-1">
                          <p className="text-sm font-medium">{entry.description}</p>
                          <p className="text-xs text-muted-foreground">
                            {client?.firstName} {client?.lastName} • {matter?.title}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {formatDistanceToNow(entry.createdAt, { addSuffix: true })}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium">
                            {(entry.duration / 60).toFixed(1)}h
                          </p>
                          <Badge variant={entry.isBillable ? "default" : "secondary"} className="text-xs">
                            {entry.isBillable ? "Billable" : "Non-billable"}
                          </Badge>
                        </div>
                      </div>
                    )
                  })
                )}
              </div>
            </CardContent>
          </Card>

          {/* Recent Matters */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Recent Matters</CardTitle>
                <CardDescription>Latest matters and cases</CardDescription>
              </div>
              <Button variant="ghost" size="sm" asChild>
                <Link href="/matters">
                  View All <ArrowRight className="w-4 h-4 ml-1" />
                </Link>
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentMatters.length === 0 ? (
                  <p className="text-sm text-muted-foreground">No matters yet</p>
                ) : (
                  recentMatters.map((matter) => {
                    const client = clients.find(c => c.id === matter.clientId)
                    
                    return (
                      <div key={matter.id} className="flex items-center justify-between">
                        <div className="space-y-1">
                          <p className="text-sm font-medium">{matter.title}</p>
                          <p className="text-xs text-muted-foreground">
                            {client?.firstName} {client?.lastName}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {formatDistanceToNow(matter.createdAt, { addSuffix: true })}
                          </p>
                        </div>
                        <div className="text-right">
                          <Badge 
                            variant={matter.status === 'open' ? "default" : "secondary"}
                            className="text-xs"
                          >
                            {matter.status}
                          </Badge>
                          <p className="text-xs text-muted-foreground mt-1">
                            {matter.matterType}
                          </p>
                        </div>
                      </div>
                    )
                  })
                )}
              </div>
            </CardContent>
          </Card>
        </div>
    </div>
    </MainLayout>
  )
}
