'use client'

import React, { useState } from 'react'
import { MainLayout } from '@/components/layout/MainLayout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { useApp } from '@/contexts/AppContext'
import { Document as DocumentType } from '@/types'
import { Search, Plus, FileText, Download, Edit, Trash2, Upload, File, Folder, Eye } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { DocumentForm } from '@/components/forms/DocumentForm'

export default function DocumentsPage() {
  const { state, deleteDocument } = useApp()
  const { documents, matters, clients } = state
  
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedDocument, setSelectedDocument] = useState<DocumentType | null>(null)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [previewDocument, setPreviewDocument] = useState<DocumentType | null>(null)
  const [isPreviewOpen, setIsPreviewOpen] = useState(false)

  // Filter documents based on search query and category
  const filteredDocuments = documents.filter(doc => {
    const query = searchQuery.toLowerCase()
    const matter = matters.find(m => m.id === doc.matterId)
    const client = matter ? clients.find(c => c.id === matter.clientId) : null
    const clientName = client ? `${client.firstName} ${client.lastName}`.toLowerCase() : ''
    
    const matchesSearch = (
      doc.name.toLowerCase().includes(query) ||
      doc.description.toLowerCase().includes(query) ||
      (matter?.title.toLowerCase().includes(query)) ||
      clientName.includes(query)
    )
    
    const matchesCategory = !selectedCategory || doc.category === selectedCategory
    
    return matchesSearch && matchesCategory
  })

  const handleEditDocument = (document: DocumentType) => {
    setSelectedDocument(document)
    setIsEditDialogOpen(true)
  }

  const handleDeleteDocument = (documentId: string) => {
    if (confirm('Are you sure you want to delete this document? This action cannot be undone.')) {
      deleteDocument(documentId)
    }
  }

  const handleDownload = (document: DocumentType) => {
    try {
      // Get the stored file data
      const fileData = localStorage.getItem(`file_${document.url}`)
      if (fileData) {
        const parsedData = JSON.parse(fileData)

        // Create a blob from the stored data
        const byteCharacters = atob(parsedData.data.split(',')[1])
        const byteNumbers = new Array(byteCharacters.length)
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i)
        }
        const byteArray = new Uint8Array(byteNumbers)
        const blob = new Blob([byteArray], { type: parsedData.type })

        // Create download link
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = document.name
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      } else {
        alert(`File not found: ${document.name}`)
      }
    } catch (error) {
      console.error('Error downloading file:', error)
      alert(`Error downloading: ${document.name}`)
    }
  }

  const handlePreview = (document: DocumentType) => {
    setPreviewDocument(document)
    setIsPreviewOpen(true)
  }

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase()
    switch (extension) {
      case 'pdf':
        return <FileText className="w-4 h-4 text-red-500" />
      case 'doc':
      case 'docx':
        return <FileText className="w-4 h-4 text-blue-500" />
      case 'xls':
      case 'xlsx':
        return <FileText className="w-4 h-4 text-green-500" />
      case 'jpg':
      case 'jpeg':
      case 'png':
        return <FileText className="w-4 h-4 text-purple-500" />
      default:
        return <File className="w-4 h-4 text-gray-500" />
    }
  }

  const getCategoryBadgeVariant = (category: string) => {
    switch (category) {
      case 'contract':
        return 'default'
      case 'legal-brief':
        return 'secondary'
      case 'evidence':
        return 'destructive'
      case 'correspondence':
        return 'outline'
      case 'court-filing':
        return 'secondary'
      default:
        return 'outline'
    }
  }

  const categories = [
    'contract',
    'legal-brief', 
    'evidence',
    'correspondence',
    'court-filing',
    'other'
  ]

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Documents</h1>
            <p className="text-muted-foreground">
              Manage client and matter documents
            </p>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Upload Document
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Upload Document</DialogTitle>
                <DialogDescription>
                  Upload a new document and associate it with a client or matter.
                </DialogDescription>
              </DialogHeader>
              <DocumentForm onSuccess={() => setIsAddDialogOpen(false)} />
            </DialogContent>
          </Dialog>
        </div>

        {/* Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Documents</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{documents.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Contracts</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {documents.filter(d => d.category === 'contract').length}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Legal Briefs</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {documents.filter(d => d.category === 'legal-brief').length}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Evidence</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {documents.filter(d => d.category === 'evidence').length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardHeader>
            <CardTitle>Search & Filter Documents</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search by name, description, matter, or client..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <select 
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 border border-input bg-background rounded-md text-sm"
              >
                <option value="">All Categories</option>
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </option>
                ))}
              </select>
            </div>
          </CardContent>
        </Card>

        {/* Documents Table */}
        <Card>
          <CardHeader>
            <CardTitle>Documents ({filteredDocuments.length})</CardTitle>
            <CardDescription>
              {searchQuery && `Showing results for "${searchQuery}"`}
              {selectedCategory && ` in category "${selectedCategory.replace('-', ' ')}"`}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Document</TableHead>
                    <TableHead>Matter/Client</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Size</TableHead>
                    <TableHead>Uploaded</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredDocuments.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <div className="text-muted-foreground">
                          {searchQuery || selectedCategory ? 'No documents found matching your criteria.' : 'No documents yet. Upload your first document to get started.'}
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredDocuments.map((document) => {
                      const matter = matters.find(m => m.id === document.matterId)
                      const client = matter ? clients.find(c => c.id === matter.clientId) : null
                      
                      return (
                        <TableRow key={document.id} className="cursor-pointer hover:bg-muted/50">
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              {getFileIcon(document.name)}
                              <div>
                                <div className="font-medium">{document.name}</div>
                                <div className="text-sm text-muted-foreground truncate max-w-xs">
                                  {document.description}
                                </div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">
                                {matter?.title || 'No Matter'}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {client ? `${client.firstName} ${client.lastName}` : 'No Client'}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant={getCategoryBadgeVariant(document.category)} className="text-xs">
                              {document.category.replace('-', ' ')}
                            </Badge>
                          </TableCell>
                          <TableCell className="font-mono text-sm">
                            {document.size ? `${(document.size / 1024).toFixed(1)} KB` : 'Unknown'}
                          </TableCell>
                          <TableCell className="text-sm text-muted-foreground">
                            {formatDistanceToNow(document.createdAt, { addSuffix: true })}
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handlePreview(document)}
                                title="Preview"
                              >
                                <Eye className="w-4 h-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDownload(document)}
                                title="Download"
                              >
                                <Download className="w-4 h-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEditDocument(document)}
                                title="Edit"
                              >
                                <Edit className="w-4 h-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteDocument(document.id)}
                                title="Delete"
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    })
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Edit Document Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Edit Document</DialogTitle>
              <DialogDescription>
                Update document information and associations.
              </DialogDescription>
            </DialogHeader>
            {selectedDocument && (
              <DocumentForm
                document={selectedDocument}
                onSuccess={() => {
                  setIsEditDialogOpen(false)
                  setSelectedDocument(null)
                }}
              />
            )}
          </DialogContent>
        </Dialog>

        {/* Preview Document Dialog */}
        <Dialog open={isPreviewOpen} onOpenChange={setIsPreviewOpen}>
          <DialogContent className="max-w-4xl max-h-[80vh]">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                {previewDocument && getFileIcon(previewDocument.name)}
                {previewDocument?.name}
              </DialogTitle>
              <DialogDescription>
                Document preview - {previewDocument?.description}
              </DialogDescription>
            </DialogHeader>
            <div className="flex-1 overflow-auto">
              {previewDocument && <DocumentPreview document={previewDocument} />}
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  )
}

// Document Preview Component
function DocumentPreview({ document }: { document: DocumentType }) {
  const [fileData, setFileData] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)

  React.useEffect(() => {
    const loadFileData = () => {
      try {
        const storedData = localStorage.getItem(`file_${document.url}`)
        if (storedData) {
          const parsedData = JSON.parse(storedData)
          setFileData(parsedData.data)
        } else {
          setError('File not found in storage')
        }
      } catch (err) {
        setError('Error loading file data')
      }
    }

    loadFileData()
  }, [document.url])

  if (error) {
    return (
      <div className="flex items-center justify-center h-64 text-muted-foreground">
        <div className="text-center">
          <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p>{error}</p>
        </div>
      </div>
    )
  }

  if (!fileData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading preview...</p>
        </div>
      </div>
    )
  }

  const fileType = document.type || 'application/octet-stream'

  // Handle different file types
  if (fileType.startsWith('image/')) {
    return (
      <div className="flex justify-center">
        <img
          src={fileData}
          alt={document.name}
          className="max-w-full max-h-96 object-contain rounded"
        />
      </div>
    )
  }

  if (fileType === 'application/pdf') {
    return (
      <div className="w-full h-96">
        <iframe
          src={fileData}
          className="w-full h-full border rounded"
          title={document.name}
        />
      </div>
    )
  }

  if (fileType === 'text/plain') {
    return (
      <div className="bg-muted p-4 rounded font-mono text-sm whitespace-pre-wrap max-h-96 overflow-auto">
        {/* For text files, we'd need to decode the base64 content */}
        <p className="text-muted-foreground">Text file preview not available in this demo</p>
      </div>
    )
  }

  // Default preview for unsupported types
  return (
    <div className="flex items-center justify-center h-64 text-muted-foreground">
      <div className="text-center">
        <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
        <p>Preview not available for this file type</p>
        <p className="text-sm mt-2">File type: {fileType}</p>
        <Button
          className="mt-4"
          onClick={() => {
            // Trigger download from preview
            const event = new CustomEvent('download-document', { detail: document })
            window.dispatchEvent(event)
          }}
        >
          <Download className="w-4 h-4 mr-2" />
          Download File
        </Button>
      </div>
    </div>
  )
}