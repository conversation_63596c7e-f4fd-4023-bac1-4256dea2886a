'use client'

import { useState } from 'react'
import { MainLayout } from '@/components/layout/MainLayout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { useApp } from '@/contexts/AppContext'
import { Matter } from '@/types'
import { Search, Plus, Briefcase, User, Edit, Trash2 } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { MatterForm } from '@/components/forms/MatterForm'

export default function MattersPage() {
  const { state, deleteMatter } = useApp()
  const { matters, clients } = state
  
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedMatter, setSelectedMatter] = useState<Matter | null>(null)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)

  // Filter matters based on search query
  const filteredMatters = matters.filter(matter => {
    const query = searchQuery.toLowerCase()
    const client = clients.find(c => c.id === matter.clientId)
    const clientName = client ? `${client.firstName} ${client.lastName}`.toLowerCase() : ''
    
    return (
      matter.title.toLowerCase().includes(query) ||
      matter.description.toLowerCase().includes(query) ||
      matter.matterType.toLowerCase().includes(query) ||
      clientName.includes(query)
    )
  })

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'open':
        return 'default'
      case 'closed':
        return 'secondary'
      case 'pending':
        return 'outline'
      case 'on-hold':
        return 'destructive'
      default:
        return 'secondary'
    }
  }

  const getMatterTypeBadgeVariant = (type: string) => {
    switch (type) {
      case 'litigation':
        return 'destructive'
      case 'corporate':
        return 'default'
      case 'real-estate':
        return 'secondary'
      case 'criminal':
        return 'outline'
      case 'family':
        return 'outline'
      case 'immigration':
        return 'secondary'
      default:
        return 'outline'
    }
  }

  const handleEditMatter = (matter: Matter) => {
    setSelectedMatter(matter)
    setIsEditDialogOpen(true)
  }

  const handleDeleteMatter = (matterId: string) => {
    if (confirm('Are you sure you want to delete this matter? This action cannot be undone.')) {
      deleteMatter(matterId)
    }
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Matters</h1>
            <p className="text-muted-foreground">
              Manage your legal matters and cases
            </p>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                New Matter
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Create New Matter</DialogTitle>
                <DialogDescription>
                  Create a new legal matter or case for a client.
                </DialogDescription>
              </DialogHeader>
              <MatterForm onSuccess={() => setIsAddDialogOpen(false)} />
            </DialogContent>
          </Dialog>
        </div>

        {/* Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Matters</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{matters.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Open</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {matters.filter(m => m.status === 'open').length}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Pending</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {matters.filter(m => m.status === 'pending').length}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Closed</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {matters.filter(m => m.status === 'closed').length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search */}
        <Card>
          <CardHeader>
            <CardTitle>Search Matters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search by title, description, type, or client..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </CardContent>
        </Card>

        {/* Matters Table */}
        <Card>
          <CardHeader>
            <CardTitle>Matters ({filteredMatters.length})</CardTitle>
            <CardDescription>
              {searchQuery && `Showing results for "${searchQuery}"`}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Matter</TableHead>
                    <TableHead>Client</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Rate</TableHead>
                    <TableHead>Hours</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredMatters.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8">
                        <div className="text-muted-foreground">
                          {searchQuery ? 'No matters found matching your search.' : 'No matters yet. Create your first matter to get started.'}
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredMatters.map((matter) => {
                      const client = clients.find(c => c.id === matter.clientId)
                      
                      return (
                        <TableRow key={matter.id} className="cursor-pointer hover:bg-muted/50">
                          <TableCell>
                            <div>
                              <div className="font-medium flex items-center">
                                <Briefcase className="w-4 h-4 mr-2 text-muted-foreground" />
                                {matter.title}
                              </div>
                              <div className="text-sm text-muted-foreground truncate max-w-xs">
                                {matter.description}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <User className="w-3 h-3 mr-2 text-muted-foreground" />
                              <div>
                                <div className="font-medium">
                                  {client ? `${client.firstName} ${client.lastName}` : 'Unknown Client'}
                                </div>
                                {client?.company && (
                                  <div className="text-sm text-muted-foreground">
                                    {client.company}
                                  </div>
                                )}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant={getMatterTypeBadgeVariant(matter.matterType)} className="text-xs">
                              {matter.matterType.replace('-', ' ')}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant={getStatusBadgeVariant(matter.status)} className="text-xs">
                              {matter.status}
                            </Badge>
                          </TableCell>
                          <TableCell className="font-mono">
                            ${matter.billableRate}/hr
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              <div>{matter.actualHours.toFixed(1)}h actual</div>
                              {matter.estimatedHours && (
                                <div className="text-muted-foreground">
                                  {matter.estimatedHours}h estimated
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="text-sm text-muted-foreground">
                            {formatDistanceToNow(matter.createdAt, { addSuffix: true })}
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEditMatter(matter)}
                              >
                                <Edit className="w-4 h-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteMatter(matter.id)}
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    })
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Edit Matter Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Edit Matter</DialogTitle>
              <DialogDescription>
                Update matter information and details.
              </DialogDescription>
            </DialogHeader>
            {selectedMatter && (
              <MatterForm 
                matter={selectedMatter}
                onSuccess={() => {
                  setIsEditDialogOpen(false)
                  setSelectedMatter(null)
                }}
              />
            )}
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  )
} 