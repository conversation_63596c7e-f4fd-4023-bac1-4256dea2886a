'use client'

import { useState } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/MainLayout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { useApp } from '@/contexts/AppContext'
import { Client, Matter, TimeEntry, Document as DocumentType } from '@/types'
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  Phone, 
  Mail, 
  MapPin, 
  Building, 
  Calendar,
  Clock,
  FileText,
  DollarSign,
  Plus,
  Briefcase
} from 'lucide-react'
import { formatDistanceToNow, format } from 'date-fns'
import { ClientForm } from '@/components/forms/ClientForm'

export default function ClientDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { state, deleteClient, getMattersByClientId, getTimeEntriesByMatterId } = useApp()
  const { clients, matters, timeEntries, documents } = state
  
  const clientId = params.id as string
  const client = clients.find(c => c.id === clientId)
  
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  
  if (!client) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="max-w-md">
            <CardHeader>
              <CardTitle>Client Not Found</CardTitle>
              <CardDescription>
                The client you're looking for doesn't exist or may have been deleted.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={() => router.push('/clients')} className="w-full">
                Back to Clients
              </Button>
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    )
  }

  const clientMatters = getMattersByClientId(clientId)
  const clientDocuments = documents.filter(d => d.clientId === clientId)
  const clientTimeEntries = timeEntries.filter(t => {
    const matter = matters.find(m => m.id === t.matterId)
    return matter?.clientId === clientId
  })

  const totalBilled = clientTimeEntries.reduce((sum, entry) => sum + (entry.billableAmount || 0), 0)
  const totalHours = clientTimeEntries.reduce((sum, entry) => sum + entry.duration, 0)

  const handleDeleteClient = () => {
    if (confirm('Are you sure you want to delete this client? This action cannot be undone and will also delete all associated matters, time entries, and documents.')) {
      deleteClient(clientId)
      router.push('/clients')
    }
  }

  const getStatusBadgeVariant = (status: Client['status']) => {
    switch (status) {
      case 'active':
        return 'default'
      case 'inactive':
        return 'secondary'
      case 'prospective':
        return 'outline'
      default:
        return 'secondary'
    }
  }

  const getMatterStatusBadgeVariant = (status: Matter['status']) => {
    switch (status) {
      case 'open':
        return 'default'
      case 'closed':
        return 'secondary'
      case 'pending':
        return 'outline'
      case 'on-hold':
        return 'destructive'
      default:
        return 'secondary'
    }
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" onClick={() => router.push('/clients')}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Clients
            </Button>
            <div>
              <h1 className="text-3xl font-bold">
                {client.firstName} {client.lastName}
              </h1>
              <p className="text-muted-foreground">
                Client details and activity overview
              </p>
            </div>
          </div>
          <div className="flex gap-2">
            <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <Edit className="w-4 h-4 mr-2" />
                  Edit Client
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Edit Client</DialogTitle>
                  <DialogDescription>
                    Update client information and contact details.
                  </DialogDescription>
                </DialogHeader>
                <ClientForm 
                  client={client}
                  onSuccess={() => setIsEditDialogOpen(false)}
                />
              </DialogContent>
            </Dialog>
            <Button variant="destructive" onClick={handleDeleteClient}>
              <Trash2 className="w-4 h-4 mr-2" />
              Delete Client
            </Button>
          </div>
        </div>

        {/* Client Overview */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Status</CardTitle>
            </CardHeader>
            <CardContent>
              <Badge variant={getStatusBadgeVariant(client.status)} className="text-sm">
                {client.status}
              </Badge>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Billed</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${totalBilled.toFixed(2)}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Hours</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalHours.toFixed(1)}h</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Active Matters</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {clientMatters.filter(m => m.status === 'open').length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Client Information */}
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Phone className="w-5 h-5 mr-2" />
                Contact Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-3">
                <Mail className="w-4 h-4 text-muted-foreground" />
                <span>{client.email}</span>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="w-4 h-4 text-muted-foreground" />
                <span>{client.phone}</span>
              </div>
              <div className="flex items-center space-x-3">
                <MapPin className="w-4 h-4 text-muted-foreground" />
                <span>
                  {client.address}, {client.city}, {client.state} {client.zipCode}
                </span>
              </div>
              {client.company && (
                <div className="flex items-center space-x-3">
                  <Building className="w-4 h-4 text-muted-foreground" />
                  <span>{client.company}</span>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="w-5 h-5 mr-2" />
                Client Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <span className="text-sm text-muted-foreground">Created:</span>
                <div>{format(client.createdAt, 'PPP')}</div>
              </div>
              <div>
                <span className="text-sm text-muted-foreground">Last Updated:</span>
                <div>{formatDistanceToNow(client.updatedAt, { addSuffix: true })}</div>
              </div>
              {client.notes && (
                <div>
                  <span className="text-sm text-muted-foreground">Notes:</span>
                  <div className="mt-1 p-3 bg-muted rounded-md text-sm">
                    {client.notes}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Client Matters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center">
                <Briefcase className="w-5 h-5 mr-2" />
                Matters ({clientMatters.length})
              </div>
              <Button size="sm" asChild>
                <a href={`/matters/new?clientId=${clientId}`}>
                  <Plus className="w-4 h-4 mr-2" />
                  New Matter
                </a>
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {clientMatters.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No matters found for this client.
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Matter</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Hours</TableHead>
                      <TableHead>Billed</TableHead>
                      <TableHead>Created</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {clientMatters.map((matter) => (
                      <TableRow key={matter.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{matter.title}</div>
                            <div className="text-sm text-muted-foreground truncate max-w-xs">
                              {matter.description}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="text-xs">
                            {matter.matterType.replace('-', ' ')}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={getMatterStatusBadgeVariant(matter.status)}>
                            {matter.status}
                          </Badge>
                        </TableCell>
                        <TableCell>{matter.actualHours.toFixed(1)}h</TableCell>
                        <TableCell>${matter.totalBilled.toFixed(2)}</TableCell>
                        <TableCell className="text-sm text-muted-foreground">
                          {formatDistanceToNow(matter.createdAt, { addSuffix: true })}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Recent Time Entries */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="w-5 h-5 mr-2" />
              Recent Time Entries
            </CardTitle>
          </CardHeader>
          <CardContent>
            {clientTimeEntries.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No time entries found for this client.
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Matter</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead>Duration</TableHead>
                      <TableHead>Rate</TableHead>
                      <TableHead>Amount</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {clientTimeEntries.slice(0, 10).map((entry) => {
                      const matter = matters.find(m => m.id === entry.matterId)
                      return (
                        <TableRow key={entry.id}>
                          <TableCell className="text-sm">
                            {format(entry.date, 'MMM d, yyyy')}
                          </TableCell>
                          <TableCell>
                            <div className="font-medium text-sm">
                              {matter?.title || 'Unknown Matter'}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm truncate max-w-xs">
                              {entry.description}
                            </div>
                          </TableCell>
                          <TableCell className="text-sm">
                            {entry.duration.toFixed(1)}h
                          </TableCell>
                          <TableCell className="text-sm">
                            ${entry.billableRate.toFixed(2)}
                          </TableCell>
                          <TableCell className="text-sm font-medium">
                            ${(entry.billableAmount || 0).toFixed(2)}
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Client Documents */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center">
                <FileText className="w-5 h-5 mr-2" />
                Documents ({clientDocuments.length})
              </div>
              <Button size="sm" asChild>
                <a href={`/documents/new?clientId=${clientId}`}>
                  <Plus className="w-4 h-4 mr-2" />
                  Upload Document
                </a>
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {clientDocuments.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No documents found for this client.
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Document</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Matter</TableHead>
                      <TableHead>Uploaded</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {clientDocuments.slice(0, 10).map((document) => {
                      const matter = matters.find(m => m.id === document.matterId)
                      return (
                        <TableRow key={document.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium text-sm">{document.name}</div>
                              <div className="text-sm text-muted-foreground truncate max-w-xs">
                                {document.description}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline" className="text-xs">
                              {document.category.replace('-', ' ')}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-sm">
                            {matter?.title || 'No Matter'}
                          </TableCell>
                          <TableCell className="text-sm text-muted-foreground">
                            {formatDistanceToNow(document.uploadedAt, { addSuffix: true })}
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
