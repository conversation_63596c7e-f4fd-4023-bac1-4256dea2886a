'use client'

import { useEffect, useState } from 'react'
import { useApp } from '@/contexts/AppContext'
import { Event } from '@/types'

interface Reminder {
  id: string
  eventId: string
  title: string
  message: string
  type: 'appointment' | 'court-date' | 'deadline' | 'meeting' | 'reminder'
  scheduledTime: Date
  eventTime: Date
  isShown: boolean
}

export function useReminders() {
  const { state } = useApp()
  const { events } = state
  const [activeReminders, setActiveReminders] = useState<Reminder[]>([])
  const [shownReminders, setShownReminders] = useState<Set<string>>(new Set())

  useEffect(() => {
    const checkReminders = () => {
      const now = new Date()
      const upcomingReminders: Reminder[] = []

      events.forEach(event => {
        // Skip if no reminder is set or already sent
        if (!event.reminderMinutes || event.reminderSent) return

        const reminderTime = new Date(event.startDate.getTime() - (event.reminderMinutes * 60 * 1000))
        
        // Check if it's time to show the reminder
        if (now >= reminderTime && now < event.startDate) {
          const reminderId = `${event.id}-${event.reminderMinutes}`
          
          // Don't show if already shown in this session
          if (shownReminders.has(reminderId)) return

          upcomingReminders.push({
            id: reminderId,
            eventId: event.id,
            title: event.title,
            message: createReminderMessage(event),
            type: event.type,
            scheduledTime: reminderTime,
            eventTime: event.startDate,
            isShown: false
          })
        }
      })

      if (upcomingReminders.length > 0) {
        setActiveReminders(prev => {
          const existingIds = new Set(prev.map(r => r.id))
          const newReminders = upcomingReminders.filter(r => !existingIds.has(r.id))
          return [...prev, ...newReminders]
        })
      }
    }

    // Check reminders every minute
    const interval = setInterval(checkReminders, 60000)
    
    // Check immediately
    checkReminders()

    return () => clearInterval(interval)
  }, [events, shownReminders])

  const createReminderMessage = (event: Event): string => {
    const timeUntil = Math.round((event.startDate.getTime() - Date.now()) / (1000 * 60))
    const timeText = timeUntil <= 60 ? `${timeUntil} minutes` : `${Math.round(timeUntil / 60)} hours`
    
    switch (event.type) {
      case 'appointment':
        return `You have an appointment "${event.title}" in ${timeText}`
      case 'court-date':
        return `Court date "${event.title}" is coming up in ${timeText}`
      case 'deadline':
        return `Deadline "${event.title}" is due in ${timeText}`
      case 'meeting':
        return `Meeting "${event.title}" starts in ${timeText}`
      default:
        return `Event "${event.title}" is scheduled in ${timeText}`
    }
  }

  const dismissReminder = (reminderId: string) => {
    setActiveReminders(prev => prev.filter(r => r.id !== reminderId))
    setShownReminders(prev => new Set([...prev, reminderId]))
  }

  const dismissAllReminders = () => {
    const reminderIds = activeReminders.map(r => r.id)
    setActiveReminders([])
    setShownReminders(prev => new Set([...prev, ...reminderIds]))
  }

  const snoozeReminder = (reminderId: string, minutes: number = 5) => {
    setActiveReminders(prev => prev.filter(r => r.id !== reminderId))
    
    // Re-show the reminder after the snooze period
    setTimeout(() => {
      const reminder = activeReminders.find(r => r.id === reminderId)
      if (reminder) {
        setActiveReminders(prev => [...prev, reminder])
      }
    }, minutes * 60 * 1000)
  }

  return {
    activeReminders,
    dismissReminder,
    dismissAllReminders,
    snoozeReminder
  }
}
