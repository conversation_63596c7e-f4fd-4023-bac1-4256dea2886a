'use client'

import { useState, useEffect, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useApp } from '@/contexts/AppContext'
import { Play, Pause, Square, Clock, Save } from 'lucide-react'

interface TimerState {
  isRunning: boolean
  seconds: number
  matterId: string
  description: string
  startTime?: Date
}

const TIMER_STORAGE_KEY = 'practice-manager-timer'

export function Timer() {
  const { state, addTimeEntry } = useApp()
  const { matters, clients } = state
  
  const [timer, setTimer] = useState<TimerState>({
    isRunning: false,
    seconds: 0,
    matterId: '',
    description: ''
  })
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  // Load timer state from localStorage on mount
  useEffect(() => {
    const savedTimer = localStorage.getItem(TIMER_STORAGE_KEY)
    if (savedTimer) {
      try {
        const parsed: TimerState = JSON.parse(savedTimer)
        
        // If timer was running, calculate elapsed time since last save
        if (parsed.isRunning && parsed.startTime) {
          const now = new Date()
          const elapsed = Math.floor((now.getTime() - new Date(parsed.startTime).getTime()) / 1000)
          parsed.seconds += elapsed
        }
        
        setTimer(parsed)
      } catch (error) {
        console.error('Failed to load timer state:', error)
      }
    }
  }, [])

  // Save timer state to localStorage
  const saveTimerState = (newTimer: TimerState) => {
    try {
      localStorage.setItem(TIMER_STORAGE_KEY, JSON.stringify(newTimer))
    } catch (error) {
      console.error('Failed to save timer state:', error)
    }
  }

  // Start the timer interval
  useEffect(() => {
    if (timer.isRunning) {
      intervalRef.current = setInterval(() => {
        setTimer(prev => {
          const newTimer = { ...prev, seconds: prev.seconds + 1 }
          saveTimerState(newTimer)
          return newTimer
        })
      }, 1000)
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [timer.isRunning])

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const remainingSeconds = seconds % 60
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const startTimer = () => {
    if (!timer.matterId) {
      alert('Please select a matter before starting the timer')
      return
    }

    const newTimer = {
      ...timer,
      isRunning: true,
      startTime: new Date()
    }
    setTimer(newTimer)
    saveTimerState(newTimer)
  }

  const pauseTimer = () => {
    const newTimer = { ...timer, isRunning: false }
    setTimer(newTimer)
    saveTimerState(newTimer)
  }

  const stopTimer = () => {
    const newTimer = {
      isRunning: false,
      seconds: 0,
      matterId: '',
      description: '',
      startTime: undefined
    }
    setTimer(newTimer)
    localStorage.removeItem(TIMER_STORAGE_KEY)
  }

  const saveTimeEntry = () => {
    if (!timer.matterId || timer.seconds === 0) {
      alert('Please select a matter and ensure timer has run before saving')
      return
    }

    const selectedMatter = matters.find(m => m.id === timer.matterId)
    if (!selectedMatter) {
      alert('Selected matter not found')
      return
    }

    const timeEntryData = {
      matterId: timer.matterId,
      clientId: selectedMatter.clientId,
      date: new Date(),
      duration: Math.round(timer.seconds / 60), // Convert to minutes
      description: timer.description || `Timer entry - ${selectedMatter.title}`,
      billableRate: selectedMatter.billableRate,
      isBillable: true,
      isInvoiced: false
    }

    addTimeEntry(timeEntryData)
    stopTimer()
    alert('Time entry saved successfully!')
  }

  const selectedMatter = matters.find(m => m.id === timer.matterId)
  const selectedClient = selectedMatter ? clients.find(c => c.id === selectedMatter.clientId) : null

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Clock className="w-5 h-5 mr-2" />
          Time Tracker
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Timer Display */}
        <div className="text-center">
          <div className="text-6xl font-mono font-bold mb-4">
            {formatTime(timer.seconds)}
          </div>
          <div className="flex justify-center space-x-2">
            {!timer.isRunning ? (
              <Button onClick={startTimer} size="lg">
                <Play className="w-4 h-4 mr-2" />
                Start
              </Button>
            ) : (
              <Button onClick={pauseTimer} variant="outline" size="lg">
                <Pause className="w-4 h-4 mr-2" />
                Pause
              </Button>
            )}
            <Button onClick={stopTimer} variant="outline" size="lg">
              <Square className="w-4 h-4 mr-2" />
              Stop
            </Button>
            {timer.seconds > 0 && (
              <Button onClick={saveTimeEntry} variant="secondary" size="lg">
                <Save className="w-4 h-4 mr-2" />
                Save Entry
              </Button>
            )}
          </div>
          {timer.isRunning && (
            <Badge variant="default" className="animate-pulse mt-2">
              <Clock className="w-3 h-3 mr-1" />
              Running
            </Badge>
          )}
        </div>

        {/* Matter Selection */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Matter</label>
          <Select 
            value={timer.matterId} 
            onValueChange={(value) => {
              const newTimer = { ...timer, matterId: value }
              setTimer(newTimer)
              saveTimerState(newTimer)
            }}
            disabled={timer.isRunning}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select a matter to track time for" />
            </SelectTrigger>
            <SelectContent>
              {matters.map((matter) => {
                const client = clients.find(c => c.id === matter.clientId)
                return (
                  <SelectItem key={matter.id} value={matter.id}>
                    {matter.title}
                    {client && ` - ${client.firstName} ${client.lastName}`}
                  </SelectItem>
                )
              })}
            </SelectContent>
          </Select>
          {selectedClient && (
            <div className="text-sm text-muted-foreground">
              Client: {selectedClient.firstName} {selectedClient.lastName}
              {selectedClient.company && ` (${selectedClient.company})`}
            </div>
          )}
        </div>

        {/* Description */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Description (Optional)</label>
          <Textarea
            placeholder="Describe the work being performed..."
            value={timer.description}
            onChange={(e) => {
              const newTimer = { ...timer, description: e.target.value }
              setTimer(newTimer)
              saveTimerState(newTimer)
            }}
            disabled={timer.isRunning}
            className="min-h-[80px]"
          />
        </div>

        {/* Timer Stats */}
        {timer.seconds > 0 && selectedMatter && (
          <div className="grid grid-cols-2 gap-4 p-4 bg-muted rounded-lg">
            <div>
              <div className="text-sm font-medium">Duration</div>
              <div className="text-lg font-bold">
                {(timer.seconds / 3600).toFixed(2)} hours
              </div>
            </div>
            <div>
              <div className="text-sm font-medium">Estimated Value</div>
              <div className="text-lg font-bold">
                ${((timer.seconds / 3600) * selectedMatter.billableRate).toFixed(2)}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
} 