'use client'

import React from 'react'

interface DataPoint {
  label: string
  value: number
  color?: string
}

interface SimpleBarChartProps {
  data: DataPoint[]
  height?: number
  className?: string
  title?: string
}

export function SimpleBarChart({ data, height = 200, className = '', title }: SimpleBarChartProps) {
  if (!data || data.length === 0) {
    return (
      <div className={`flex items-center justify-center ${className}`} style={{ height }}>
        <p className="text-muted-foreground">No data available</p>
      </div>
    )
  }

  const maxValue = Math.max(...data.map(d => d.value))
  const barWidth = Math.max(40, (100 / data.length) - 2)

  return (
    <div className={className}>
      {title && <h3 className="text-lg font-semibold mb-4">{title}</h3>}
      <div className="flex items-end justify-center gap-2" style={{ height }}>
        {data.map((item, index) => {
          const barHeight = maxValue > 0 ? (item.value / maxValue) * (height - 40) : 0
          
          return (
            <div key={index} className="flex flex-col items-center">
              <div className="text-xs text-muted-foreground mb-1">
                ${item.value.toLocaleString()}
              </div>
              <div
                className={`rounded-t transition-all duration-300 hover:opacity-80 ${
                  item.color || 'bg-blue-500'
                }`}
                style={{
                  width: `${barWidth}px`,
                  height: `${barHeight}px`,
                  minHeight: item.value > 0 ? '4px' : '0px'
                }}
                title={`${item.label}: $${item.value.toLocaleString()}`}
              />
              <div className="text-xs text-center mt-2 max-w-[60px] truncate">
                {item.label}
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}

interface SimpleLineChartProps {
  data: DataPoint[]
  height?: number
  className?: string
  title?: string
}

export function SimpleLineChart({ data, height = 200, className = '', title }: SimpleLineChartProps) {
  if (!data || data.length === 0) {
    return (
      <div className={`flex items-center justify-center ${className}`} style={{ height }}>
        <p className="text-muted-foreground">No data available</p>
      </div>
    )
  }

  const maxValue = Math.max(...data.map(d => d.value))
  const minValue = Math.min(...data.map(d => d.value))
  const range = maxValue - minValue || 1

  const chartHeight = height - 60
  const chartWidth = 300
  const pointSpacing = chartWidth / (data.length - 1 || 1)

  const points = data.map((item, index) => {
    const x = index * pointSpacing
    const y = chartHeight - ((item.value - minValue) / range) * chartHeight
    return { x, y, ...item }
  })

  const pathData = points.map((point, index) => 
    `${index === 0 ? 'M' : 'L'} ${point.x} ${point.y}`
  ).join(' ')

  return (
    <div className={className}>
      {title && <h3 className="text-lg font-semibold mb-4">{title}</h3>}
      <div className="relative" style={{ height }}>
        <svg width={chartWidth} height={chartHeight} className="overflow-visible">
          {/* Grid lines */}
          {[0, 0.25, 0.5, 0.75, 1].map(ratio => (
            <line
              key={ratio}
              x1={0}
              y1={chartHeight * ratio}
              x2={chartWidth}
              y2={chartHeight * ratio}
              stroke="#e5e7eb"
              strokeWidth={1}
              strokeDasharray="2,2"
            />
          ))}
          
          {/* Line */}
          <path
            d={pathData}
            fill="none"
            stroke="#3b82f6"
            strokeWidth={2}
            className="drop-shadow-sm"
          />
          
          {/* Points */}
          {points.map((point, index) => (
            <circle
              key={index}
              cx={point.x}
              cy={point.y}
              r={4}
              fill="#3b82f6"
              className="hover:r-6 transition-all cursor-pointer"
              title={`${point.label}: $${point.value.toLocaleString()}`}
            />
          ))}
        </svg>
        
        {/* Labels */}
        <div className="flex justify-between mt-2">
          {data.map((item, index) => (
            <div key={index} className="text-xs text-center text-muted-foreground">
              {item.label}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

interface DonutChartProps {
  data: DataPoint[]
  size?: number
  className?: string
  title?: string
}

export function SimpleDonutChart({ data, size = 200, className = '', title }: DonutChartProps) {
  if (!data || data.length === 0) {
    return (
      <div className={`flex items-center justify-center ${className}`} style={{ width: size, height: size }}>
        <p className="text-muted-foreground">No data available</p>
      </div>
    )
  }

  const total = data.reduce((sum, item) => sum + item.value, 0)
  const radius = size / 2 - 20
  const innerRadius = radius * 0.6
  const center = size / 2

  let currentAngle = 0
  const segments = data.map((item, index) => {
    const percentage = item.value / total
    const angle = percentage * 2 * Math.PI
    const startAngle = currentAngle
    const endAngle = currentAngle + angle
    currentAngle = endAngle

    const x1 = center + radius * Math.cos(startAngle)
    const y1 = center + radius * Math.sin(startAngle)
    const x2 = center + radius * Math.cos(endAngle)
    const y2 = center + radius * Math.sin(endAngle)
    
    const x3 = center + innerRadius * Math.cos(endAngle)
    const y3 = center + innerRadius * Math.sin(endAngle)
    const x4 = center + innerRadius * Math.cos(startAngle)
    const y4 = center + innerRadius * Math.sin(startAngle)

    const largeArcFlag = angle > Math.PI ? 1 : 0

    const pathData = [
      `M ${x1} ${y1}`,
      `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
      `L ${x3} ${y3}`,
      `A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 0 ${x4} ${y4}`,
      'Z'
    ].join(' ')

    const colors = [
      '#3b82f6', '#ef4444', '#10b981', '#f59e0b', 
      '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'
    ]

    return {
      pathData,
      color: item.color || colors[index % colors.length],
      percentage: Math.round(percentage * 100),
      ...item
    }
  })

  return (
    <div className={className}>
      {title && <h3 className="text-lg font-semibold mb-4">{title}</h3>}
      <div className="flex items-center gap-4">
        <svg width={size} height={size}>
          {segments.map((segment, index) => (
            <path
              key={index}
              d={segment.pathData}
              fill={segment.color}
              className="hover:opacity-80 transition-opacity cursor-pointer"
              title={`${segment.label}: ${segment.percentage}%`}
            />
          ))}
        </svg>
        
        <div className="space-y-2">
          {segments.map((segment, index) => (
            <div key={index} className="flex items-center gap-2 text-sm">
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: segment.color }}
              />
              <span>{segment.label}</span>
              <span className="text-muted-foreground">
                {segment.percentage}%
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
