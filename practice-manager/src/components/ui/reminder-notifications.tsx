'use client'

import React from 'react'
import { useReminders } from '@/hooks/useReminders'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './card'
import { Button } from './button'
import { Badge } from './badge'
import { X, Clock, Calendar, AlertTriangle, Users, Briefcase, Snooze } from 'lucide-react'
import { format } from 'date-fns'

export function ReminderNotifications() {
  const { activeReminders, dismissReminder, dismissAllReminders, snoozeReminder } = useReminders()

  if (activeReminders.length === 0) {
    return null
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'appointment':
        return <Users className="w-4 h-4" />
      case 'court-date':
        return <Briefcase className="w-4 h-4" />
      case 'deadline':
        return <AlertTriangle className="w-4 h-4" />
      case 'meeting':
        return <Users className="w-4 h-4" />
      default:
        return <Calendar className="w-4 h-4" />
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'appointment':
        return 'bg-blue-500'
      case 'court-date':
        return 'bg-red-500'
      case 'deadline':
        return 'bg-orange-500'
      case 'meeting':
        return 'bg-green-500'
      default:
        return 'bg-gray-500'
    }
  }

  return (
    <div className="fixed top-4 right-4 z-50 space-y-3 max-w-sm">
      {/* Header with dismiss all button */}
      {activeReminders.length > 1 && (
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium text-muted-foreground">
            {activeReminders.length} reminders
          </span>
          <Button
            variant="ghost"
            size="sm"
            onClick={dismissAllReminders}
            className="text-xs"
          >
            Dismiss All
          </Button>
        </div>
      )}

      {/* Individual reminder cards */}
      {activeReminders.map((reminder) => (
        <Card key={reminder.id} className="shadow-lg border-l-4 border-l-blue-500 animate-in slide-in-from-right-full">
          <CardHeader className="pb-3">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-2">
                <div className={`p-1 rounded-full text-white ${getTypeColor(reminder.type)}`}>
                  {getTypeIcon(reminder.type)}
                </div>
                <div>
                  <CardTitle className="text-sm font-medium">
                    {reminder.title}
                  </CardTitle>
                  <Badge variant="outline" className="text-xs mt-1">
                    {reminder.type.replace('-', ' ')}
                  </Badge>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => dismissReminder(reminder.id)}
                className="h-6 w-6 p-0"
              >
                <X className="w-3 h-3" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <CardDescription className="text-sm mb-3">
              {reminder.message}
            </CardDescription>
            
            <div className="flex items-center gap-2 text-xs text-muted-foreground mb-3">
              <Clock className="w-3 h-3" />
              <span>
                Scheduled for {format(reminder.eventTime, 'MMM d, h:mm a')}
              </span>
            </div>

            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => snoozeReminder(reminder.id, 5)}
                className="flex-1 text-xs"
              >
                <Snooze className="w-3 h-3 mr-1" />
                Snooze 5m
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => snoozeReminder(reminder.id, 15)}
                className="flex-1 text-xs"
              >
                Snooze 15m
              </Button>
              <Button
                size="sm"
                onClick={() => dismissReminder(reminder.id)}
                className="flex-1 text-xs"
              >
                Dismiss
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

// Upcoming reminders widget for dashboard
export function UpcomingReminders() {
  const { activeReminders } = useReminders()

  if (activeReminders.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <Clock className="w-4 h-4" />
            Upcoming Reminders
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">No upcoming reminders</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-sm font-medium flex items-center gap-2">
          <Clock className="w-4 h-4" />
          Upcoming Reminders ({activeReminders.length})
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {activeReminders.slice(0, 3).map((reminder) => (
            <div key={reminder.id} className="flex items-center gap-3 p-2 rounded border">
              <div className={`p-1 rounded-full text-white ${getTypeColor(reminder.type)}`}>
                {getTypeIcon(reminder.type)}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{reminder.title}</p>
                <p className="text-xs text-muted-foreground">
                  {format(reminder.eventTime, 'MMM d, h:mm a')}
                </p>
              </div>
              <Badge variant="outline" className="text-xs">
                {reminder.type.replace('-', ' ')}
              </Badge>
            </div>
          ))}
          {activeReminders.length > 3 && (
            <p className="text-xs text-muted-foreground text-center">
              +{activeReminders.length - 3} more reminders
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  )

  function getTypeIcon(type: string) {
    switch (type) {
      case 'appointment':
        return <Users className="w-3 h-3" />
      case 'court-date':
        return <Briefcase className="w-3 h-3" />
      case 'deadline':
        return <AlertTriangle className="w-3 h-3" />
      case 'meeting':
        return <Users className="w-3 h-3" />
      default:
        return <Calendar className="w-3 h-3" />
    }
  }

  function getTypeColor(type: string) {
    switch (type) {
      case 'appointment':
        return 'bg-blue-500'
      case 'court-date':
        return 'bg-red-500'
      case 'deadline':
        return 'bg-orange-500'
      case 'meeting':
        return 'bg-green-500'
      default:
        return 'bg-gray-500'
    }
  }
}
