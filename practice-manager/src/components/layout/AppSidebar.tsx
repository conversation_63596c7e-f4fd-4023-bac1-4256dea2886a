'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { 
  Users, 
  Briefcase, 
  Clock, 
  FileText, 
  Calendar, 
  DollarSign, 
  Home,
  Settings,
  Search
} from 'lucide-react'
import { cn } from '@/lib/utils'
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarHeader,
  SidebarFooter,
} from '@/components/ui/sidebar'
import { Button } from '@/components/ui/button'

const navigationItems = [
  {
    title: "Main",
    items: [
      {
        title: "Dashboard",
        url: "/",
        icon: Home,
      },
      {
        title: "Clients",
        url: "/clients",
        icon: Users,
      },
      {
        title: "Matters",
        url: "/matters",
        icon: Briefcase,
      },
      {
        title: "Time Tracking",
        url: "/time",
        icon: Clock,
      },
    ],
  },
  {
    title: "Management",
    items: [
      {
        title: "Documents",
        url: "/documents",
        icon: FileText,
      },
      {
        title: "Calendar",
        url: "/calendar",
        icon: Calendar,
      },
      {
        title: "Billing",
        url: "/billing",
        icon: DollarSign,
      },
    ],
  },
]

export function AppSidebar() {
  const pathname = usePathname()

  return (
    <Sidebar>
      <SidebarHeader className="p-4">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
            <Briefcase className="w-4 h-4 text-primary-foreground" />
          </div>
          <div>
            <h2 className="text-lg font-semibold">Practice Manager</h2>
            <p className="text-xs text-muted-foreground">Law Firm Management</p>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent>
        <div className="px-4 py-2">
          <Button variant="outline" className="w-full justify-start" size="sm">
            <Search className="w-4 h-4 mr-2" />
            Quick Search
          </Button>
        </div>

        {navigationItems.map((group) => (
          <SidebarGroup key={group.title}>
            <SidebarGroupLabel>{group.title}</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {group.items.map((item) => (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton 
                      asChild 
                      isActive={pathname === item.url}
                    >
                      <Link href={item.url}>
                        <item.icon className="w-4 h-4" />
                        <span>{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}
      </SidebarContent>

      <SidebarFooter className="p-4">
        <Button variant="ghost" className="w-full justify-start" size="sm">
          <Settings className="w-4 h-4 mr-2" />
          Settings
        </Button>
      </SidebarFooter>
    </Sidebar>
  )
} 