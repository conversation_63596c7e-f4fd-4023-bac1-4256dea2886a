'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { useAuth } from '@/contexts/AuthContext'
import { User, LogOut, Settings, Shield } from 'lucide-react'

export function UserMenu() {
  const router = useRouter()
  const { user, logout, isAuthenticated } = useAuth()
  const [isProfileOpen, setIsProfileOpen] = useState(false)

  if (!isAuthenticated || !user) {
    return (
      <Button 
        variant="outline" 
        onClick={() => router.push('/login')}
      >
        Sign In
      </Button>
    )
  }

  const handleLogout = () => {
    logout()
    router.push('/login')
  }

  const getUserInitials = () => {
    return `${user.firstName.charAt(0)}${user.lastName.charAt(0)}`.toUpperCase()
  }

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'admin':
        return 'default'
      case 'attorney':
        return 'secondary'
      case 'paralegal':
        return 'outline'
      case 'staff':
        return 'outline'
      default:
        return 'outline'
    }
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="relative h-8 w-8 rounded-full">
            <Avatar className="h-8 w-8">
              <AvatarFallback className="text-xs">
                {getUserInitials()}
              </AvatarFallback>
            </Avatar>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56" align="end" forceMount>
          <DropdownMenuLabel className="font-normal">
            <div className="flex flex-col space-y-1">
              <p className="text-sm font-medium leading-none">
                {user.firstName} {user.lastName}
              </p>
              <p className="text-xs leading-none text-muted-foreground">
                {user.email}
              </p>
              <Badge variant={getRoleBadgeVariant(user.role)} className="w-fit text-xs mt-1">
                {user.role}
              </Badge>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => setIsProfileOpen(true)}>
            <User className="mr-2 h-4 w-4" />
            <span>Profile</span>
          </DropdownMenuItem>
          <DropdownMenuItem>
            <Settings className="mr-2 h-4 w-4" />
            <span>Settings</span>
          </DropdownMenuItem>
          {user.role === 'admin' && (
            <DropdownMenuItem>
              <Shield className="mr-2 h-4 w-4" />
              <span>Admin Panel</span>
            </DropdownMenuItem>
          )}
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={handleLogout}>
            <LogOut className="mr-2 h-4 w-4" />
            <span>Log out</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Profile Dialog */}
      <Dialog open={isProfileOpen} onOpenChange={setIsProfileOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>User Profile</DialogTitle>
            <DialogDescription>
              Your account information and role details.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <Avatar className="h-16 w-16">
                <AvatarFallback className="text-lg">
                  {getUserInitials()}
                </AvatarFallback>
              </Avatar>
              <div>
                <h3 className="text-lg font-medium">
                  {user.firstName} {user.lastName}
                </h3>
                <p className="text-sm text-muted-foreground">{user.email}</p>
                <Badge variant={getRoleBadgeVariant(user.role)} className="mt-1">
                  {user.role}
                </Badge>
              </div>
            </div>
            
            <div className="space-y-2">
              <div>
                <span className="text-sm font-medium">User ID:</span>
                <p className="text-sm text-muted-foreground">{user.id}</p>
              </div>
              <div>
                <span className="text-sm font-medium">Account Created:</span>
                <p className="text-sm text-muted-foreground">
                  {user.createdAt.toLocaleDateString()}
                </p>
              </div>
              <div>
                <span className="text-sm font-medium">Role Permissions:</span>
                <div className="text-sm text-muted-foreground">
                  {user.role === 'admin' && (
                    <ul className="list-disc list-inside space-y-1">
                      <li>Full system access</li>
                      <li>User management</li>
                      <li>System configuration</li>
                    </ul>
                  )}
                  {user.role === 'attorney' && (
                    <ul className="list-disc list-inside space-y-1">
                      <li>Client management</li>
                      <li>Matter management</li>
                      <li>Time tracking</li>
                      <li>Document access</li>
                    </ul>
                  )}
                  {user.role === 'paralegal' && (
                    <ul className="list-disc list-inside space-y-1">
                      <li>Limited client access</li>
                      <li>Document management</li>
                      <li>Time tracking</li>
                    </ul>
                  )}
                  {user.role === 'staff' && (
                    <ul className="list-disc list-inside space-y-1">
                      <li>Basic system access</li>
                      <li>Time tracking</li>
                    </ul>
                  )}
                </div>
              </div>
            </div>

            <div className="flex justify-end pt-4">
              <Button onClick={() => setIsProfileOpen(false)}>
                Close
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
