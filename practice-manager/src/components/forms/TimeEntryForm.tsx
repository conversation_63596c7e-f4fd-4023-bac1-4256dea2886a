'use client'

import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { useApp } from '@/contexts/AppContext'
import { TimeEntry, TimeEntryFormData } from '@/types'

const timeEntrySchema = z.object({
  matterId: z.string().min(1, 'Matter is required'),
  date: z.date(),
  duration: z.number().min(1, 'Duration must be at least 1 minute'),
  description: z.string().min(1, 'Description is required'),
  billableRate: z.number().min(0, 'Rate must be positive'),
  isBillable: z.boolean()
})

interface TimeEntryFormProps {
  timeEntry?: TimeEntry
  onSuccess: () => void
}

export function TimeEntryForm({ timeEntry, onSuccess }: TimeEntryFormProps) {
  const { addTimeEntry, updateTimeEntry, state } = useApp()
  const { matters, clients } = state
  
  const form = useForm<TimeEntryFormData>({
    resolver: zodResolver(timeEntrySchema),
    defaultValues: {
      matterId: timeEntry?.matterId || '',
      date: timeEntry?.date || new Date(),
      duration: timeEntry?.duration || 60, // Default to 1 hour (60 minutes)
      description: timeEntry?.description || '',
      billableRate: timeEntry?.billableRate || 250,
      isBillable: timeEntry?.isBillable ?? true
    }
  })

  const selectedMatterId = form.watch('matterId')
  const selectedMatter = matters.find(m => m.id === selectedMatterId)
  const selectedClient = selectedMatter ? clients.find(c => c.id === selectedMatter.clientId) : null

  // Update billable rate when matter changes
  const handleMatterChange = (matterId: string) => {
    const matter = matters.find(m => m.id === matterId)
    if (matter) {
      form.setValue('billableRate', matter.billableRate)
    }
  }

  const onSubmit = (data: TimeEntryFormData) => {
    try {
      const timeEntryData = {
        ...data,
        clientId: selectedMatter?.clientId || '',
        isInvoiced: false
      }

      if (timeEntry) {
        // Update existing time entry
        updateTimeEntry(timeEntry.id, timeEntryData)
      } else {
        // Add new time entry
        addTimeEntry(timeEntryData)
      }
      onSuccess()
      form.reset()
    } catch (error) {
      console.error('Error saving time entry:', error)
    }
  }

  const formatHoursToMinutes = (hours: number): number => {
    return Math.round(hours * 60)
  }

  const formatMinutesToHours = (minutes: number): number => {
    return minutes / 60
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Matter Selection */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Matter Information</h3>
          
          <FormField
            control={form.control}
            name="matterId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Matter</FormLabel>
                <Select 
                  onValueChange={(value) => {
                    field.onChange(value)
                    handleMatterChange(value)
                  }} 
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a matter" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {matters.map((matter) => {
                      const client = clients.find(c => c.id === matter.clientId)
                      return (
                        <SelectItem key={matter.id} value={matter.id}>
                          {matter.title}
                          {client && ` - ${client.firstName} ${client.lastName}`}
                        </SelectItem>
                      )
                    })}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {selectedClient && (
            <div className="text-sm text-muted-foreground">
              Client: {selectedClient.firstName} {selectedClient.lastName}
              {selectedClient.company && ` (${selectedClient.company})`}
            </div>
          )}
        </div>

        {/* Time Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Time Details</h3>
          
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="date"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Date</FormLabel>
                  <FormControl>
                    <Input 
                      type="date" 
                      value={field.value instanceof Date ? field.value.toISOString().split('T')[0] : ''}
                      onChange={(e) => field.onChange(new Date(e.target.value))}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="duration"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Duration (hours)</FormLabel>
                  <FormControl>
                    <Input 
                      type="number" 
                      step="0.25"
                      placeholder="1.5"
                      value={formatMinutesToHours(field.value)}
                      onChange={(e) => field.onChange(formatHoursToMinutes(parseFloat(e.target.value) || 0))}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea 
                    placeholder="Describe the work performed..."
                    className="min-h-[100px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Billing Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Billing Details</h3>
          
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="billableRate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Billable Rate (per hour)</FormLabel>
                  <FormControl>
                    <Input 
                      type="number" 
                      placeholder="250"
                      {...field}
                      onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="isBillable"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>
                      Billable Time
                    </FormLabel>
                    <p className="text-sm text-muted-foreground">
                      Check if this time should be billed to the client
                    </p>
                  </div>
                </FormItem>
              )}
            />
          </div>

          {/* Calculate and display total value */}
          {form.watch('duration') && form.watch('billableRate') && form.watch('isBillable') && (
            <div className="p-4 bg-muted rounded-lg">
              <div className="text-sm font-medium">Total Value</div>
              <div className="text-2xl font-bold">
                ${((formatMinutesToHours(form.watch('duration')) * form.watch('billableRate'))).toFixed(2)}
              </div>
            </div>
          )}
        </div>

        {/* Form Actions */}
        <div className="flex justify-end gap-3 pt-4 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={() => {
              form.reset()
              onSuccess()
            }}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={form.formState.isSubmitting}>
            {form.formState.isSubmitting 
              ? 'Saving...' 
              : timeEntry 
                ? 'Update Time Entry' 
                : 'Add Time Entry'
            }
          </Button>
        </div>
      </form>
    </Form>
  )
} 