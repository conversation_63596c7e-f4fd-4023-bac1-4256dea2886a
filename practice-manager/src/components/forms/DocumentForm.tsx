'use client'

import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { useApp } from '@/contexts/AppContext'
import { Document, DocumentFormData } from '@/types'
import { Upload, FileText, X, AlertCircle } from 'lucide-react'
import { useState, useCallback } from 'react'
import { LoadingSpinner } from '@/components/ui/loading-spinner'

const documentSchema = z.object({
  clientId: z.string().optional(),
  matterId: z.string().optional(),
  name: z.string().min(1, 'Document name is required'),
  description: z.string().optional(),
  type: z.string().min(1, 'File type is required'),
  category: z.enum(['contract', 'correspondence', 'court-filing', 'evidence', 'legal-brief', 'memo', 'other']),
  tags: z.array(z.string())
}).refine(data => data.clientId || data.matterId, {
  message: "Either client or matter must be selected"
})

interface DocumentFormProps {
  document?: Document
  onSuccess: () => void
}

export function DocumentForm({ document, onSuccess }: DocumentFormProps) {
  const { addDocument, updateDocument, state } = useApp()
  const { clients, matters } = state

  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [tags, setTags] = useState<string[]>(document?.tags || [])
  const [currentTag, setCurrentTag] = useState('')
  const [uploadError, setUploadError] = useState<string | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [dragActive, setDragActive] = useState(false)

  // File validation constants
  const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB
  const ALLOWED_TYPES = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'text/plain'
  ]
  
  const form = useForm<DocumentFormData>({
    resolver: zodResolver(documentSchema),
    defaultValues: {
      clientId: document?.clientId || '',
      matterId: document?.matterId || '',
      name: document?.name || '',
      description: document?.description || '',
      type: document?.type || '',
      category: document?.category || 'other',
      tags: document?.tags || []
    }
  })

  const validateFile = (file: File): string | null => {
    if (file.size > MAX_FILE_SIZE) {
      return `File size must be less than ${MAX_FILE_SIZE / 1024 / 1024}MB`
    }

    if (!ALLOWED_TYPES.includes(file.type)) {
      return 'File type not supported. Please upload PDF, DOC, XLS, or image files.'
    }

    return null
  }

  const handleFileSelect = (file: File) => {
    setUploadError(null)

    const error = validateFile(file)
    if (error) {
      setUploadError(error)
      return
    }

    setSelectedFile(file)
    // Auto-fill document name and type from file
    form.setValue('name', file.name)
    form.setValue('type', file.type || 'application/octet-stream')
  }

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  // Drag and drop handlers
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0])
    }
  }, [])

  const addTag = () => {
    if (currentTag.trim() && !tags.includes(currentTag.trim())) {
      const newTags = [...tags, currentTag.trim()]
      setTags(newTags)
      form.setValue('tags', newTags)
      setCurrentTag('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    const newTags = tags.filter(tag => tag !== tagToRemove)
    setTags(newTags)
    form.setValue('tags', newTags)
  }

  const simulateFileUpload = async (file: File): Promise<string> => {
    // Simulate file upload delay
    await new Promise(resolve => setTimeout(resolve, 1000))

    // In a real app, you would upload to a server or cloud storage
    // For the MVP, we'll simulate this by creating a local URL
    const fileUrl = `uploads/${Date.now()}_${file.name}`

    // Store file data in localStorage for the MVP
    const reader = new FileReader()
    return new Promise((resolve, reject) => {
      reader.onload = () => {
        try {
          const fileData = {
            name: file.name,
            type: file.type,
            size: file.size,
            data: reader.result as string,
            uploadDate: new Date().toISOString()
          }
          localStorage.setItem(`file_${fileUrl}`, JSON.stringify(fileData))
          resolve(fileUrl)
        } catch (error) {
          reject(error)
        }
      }
      reader.onerror = () => reject(new Error('Failed to read file'))
      reader.readAsDataURL(file)
    })
  }

  const onSubmit = async (data: DocumentFormData) => {
    try {
      setIsUploading(true)
      setUploadError(null)

      if (!document && !selectedFile) {
        setUploadError('Please select a file to upload')
        return
      }

      let fileUrl = document?.url || ''

      if (selectedFile) {
        fileUrl = await simulateFileUpload(selectedFile)
      }

      const documentData = {
        ...data,
        size: selectedFile?.size || document?.size || 0,
        url: fileUrl,
        uploadDate: new Date(),
        tags,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      if (document) {
        // Update existing document
        await updateDocument(document.id, documentData)
      } else {
        // Add new document
        await addDocument(documentData)
      }

      onSuccess()
      form.reset()
      setSelectedFile(null)
      setTags([])
    } catch (error) {
      console.error('Error saving document:', error)
      setUploadError(error instanceof Error ? error.message : 'Failed to save document')
    } finally {
      setIsUploading(false)
    }
  }

  const selectedMatterId = form.watch('matterId')
  const selectedMatter = matters.find(m => m.id === selectedMatterId)
  const selectedClient = selectedMatter ? clients.find(c => c.id === selectedMatter.clientId) : null

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* File Upload Section */}
        {!document && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium">File Upload</h3>

            <div
              className={`border-2 border-dashed rounded-lg p-6 transition-colors ${
                dragActive
                  ? 'border-blue-400 bg-blue-50'
                  : uploadError
                    ? 'border-red-300 bg-red-50'
                    : 'border-gray-300 hover:border-gray-400'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <div className="text-center">
                {isUploading ? (
                  <div className="flex flex-col items-center">
                    <LoadingSpinner size="lg" />
                    <p className="mt-2 text-sm text-gray-600">Uploading...</p>
                  </div>
                ) : (
                  <>
                    <Upload className={`mx-auto h-12 w-12 ${uploadError ? 'text-red-400' : 'text-gray-400'}`} />
                    <div className="mt-4">
                      <label htmlFor="file-upload" className="cursor-pointer">
                        <span className="mt-2 block text-sm font-medium text-gray-900">
                          {selectedFile ? selectedFile.name : 'Choose a file or drag and drop'}
                        </span>
                        <input
                          id="file-upload"
                          name="file-upload"
                          type="file"
                          className="sr-only"
                          onChange={handleInputChange}
                          accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.gif,.txt"
                          disabled={isUploading}
                        />
                      </label>
                      <p className="mt-1 text-xs text-gray-500">
                        PDF, DOC, XLS, TXT, or image files up to 10MB
                      </p>
                    </div>
                  </>
                )}
              </div>

              {selectedFile && !isUploading && (
                <div className="mt-4 flex items-center justify-between bg-gray-50 p-3 rounded">
                  <div className="flex items-center">
                    <FileText className="w-4 h-4 text-gray-500 mr-2" />
                    <span className="text-sm text-gray-700">{selectedFile.name}</span>
                    <span className="text-xs text-gray-500 ml-2">
                      ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
                    </span>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setSelectedFile(null)
                      setUploadError(null)
                      form.setValue('name', '')
                      form.setValue('type', '')
                    }}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              )}

              {uploadError && (
                <div className="mt-4 flex items-center text-red-600 text-sm">
                  <AlertCircle className="w-4 h-4 mr-2" />
                  {uploadError}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Document Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Document Information</h3>
          
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Document Name</FormLabel>
                <FormControl>
                  <Input placeholder="Contract Agreement.pdf" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description (Optional)</FormLabel>
                <FormControl>
                  <Textarea 
                    placeholder="Brief description of the document..."
                    className="min-h-[80px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="category"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Category</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select document category" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="contract">Contract</SelectItem>
                    <SelectItem value="correspondence">Correspondence</SelectItem>
                    <SelectItem value="court-filing">Court Filing</SelectItem>
                    <SelectItem value="evidence">Evidence</SelectItem>
                    <SelectItem value="legal-brief">Legal Brief</SelectItem>
                    <SelectItem value="memo">Memo</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Association */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Associate with</h3>
          
          <FormField
            control={form.control}
            name="matterId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Matter (Optional)</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a matter" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="">No specific matter</SelectItem>
                    {matters.map((matter) => {
                      const client = clients.find(c => c.id === matter.clientId)
                      return (
                        <SelectItem key={matter.id} value={matter.id}>
                          {matter.title}
                          {client && ` - ${client.firstName} ${client.lastName}`}
                        </SelectItem>
                      )
                    })}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {!selectedMatter && (
            <FormField
              control={form.control}
              name="clientId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Client (if no matter selected)</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a client" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="">No client</SelectItem>
                      {clients.map((client) => (
                        <SelectItem key={client.id} value={client.id}>
                          {client.firstName} {client.lastName}
                          {client.company && ` (${client.company})`}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          {selectedClient && (
            <div className="text-sm text-muted-foreground">
              Client: {selectedClient.firstName} {selectedClient.lastName}
              {selectedClient.company && ` (${selectedClient.company})`}
            </div>
          )}
        </div>

        {/* Tags */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Tags</h3>
          
          <div className="flex gap-2">
            <Input
              placeholder="Add a tag..."
              value={currentTag}
              onChange={(e) => setCurrentTag(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
            />
            <Button type="button" onClick={addTag} variant="outline">
              Add Tag
            </Button>
          </div>

          {tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {tags.map((tag, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800"
                >
                  {tag}
                  <button
                    type="button"
                    onClick={() => removeTag(tag)}
                    className="ml-1 text-blue-600 hover:text-blue-800"
                  >
                    ×
                  </button>
                </span>
              ))}
            </div>
          )}
        </div>

        {/* Form Actions */}
        <div className="flex justify-end gap-3 pt-4 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={() => {
              form.reset()
              setSelectedFile(null)
              setTags([])
              onSuccess()
            }}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={form.formState.isSubmitting || isUploading}>
            {form.formState.isSubmitting || isUploading ? (
              <div className="flex items-center">
                <LoadingSpinner size="sm" className="mr-2" />
                {isUploading ? 'Uploading...' : 'Saving...'}
              </div>
            ) : document ? (
              'Update Document'
            ) : (
              'Upload Document'
            )}
          </Button>
        </div>
      </form>
    </Form>
  )
} 