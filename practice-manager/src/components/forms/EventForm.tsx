'use client'

import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { useApp } from '@/contexts/AppContext'
import { Event, EventFormData } from '@/types'

const eventSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  startDate: z.date(),
  endDate: z.date(),
  type: z.enum(['appointment', 'court-date', 'deadline', 'meeting', 'reminder']),
  clientId: z.string().optional(),
  matterId: z.string().optional(),
  location: z.string().optional(),
  allDay: z.boolean(),
  reminderMinutes: z.number().optional()
}).refine(data => data.endDate >= data.startDate, {
  message: "End date must be after start date",
  path: ["endDate"]
})

interface EventFormProps {
  event?: Event
  onSuccess: () => void
}

export function EventForm({ event, onSuccess }: EventFormProps) {
  const { addEvent, updateEvent, state } = useApp()
  const { clients, matters } = state
  
  const form = useForm<EventFormData>({
    resolver: zodResolver(eventSchema),
    defaultValues: {
      title: event?.title || '',
      description: event?.description || '',
      startDate: event?.startDate || new Date(),
      endDate: event?.endDate || new Date(),
      type: event?.type || 'appointment',
      clientId: event?.clientId || '',
      matterId: event?.matterId || '',
      location: event?.location || '',
      allDay: event?.allDay || false,
      reminderMinutes: event?.reminderMinutes || undefined
    }
  })

  const selectedMatterId = form.watch('matterId')
  const selectedMatter = matters.find(m => m.id === selectedMatterId)
  const selectedClient = selectedMatter ? clients.find(c => c.id === selectedMatter.clientId) : 
                       form.watch('clientId') ? clients.find(c => c.id === form.watch('clientId')) : null

  const onSubmit = (data: EventFormData) => {
    try {
      if (event) {
        // Update existing event
        updateEvent(event.id, data)
      } else {
        // Add new event
        addEvent(data)
      }
      onSuccess()
      form.reset()
    } catch (error) {
      console.error('Error saving event:', error)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Basic Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Event Details</h3>
          
          <FormField
            control={form.control}
            name="title"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Title</FormLabel>
                <FormControl>
                  <Input placeholder="Meeting with client, Court hearing, etc." {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description (Optional)</FormLabel>
                <FormControl>
                  <Textarea 
                    placeholder="Additional details about the event..."
                    className="min-h-[80px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Event Type</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select event type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="appointment">Appointment</SelectItem>
                    <SelectItem value="court-date">Court Date</SelectItem>
                    <SelectItem value="deadline">Deadline</SelectItem>
                    <SelectItem value="meeting">Meeting</SelectItem>
                    <SelectItem value="reminder">Reminder</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Date and Time */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Schedule</h3>
          
          <FormField
            control={form.control}
            name="allDay"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>
                    All Day Event
                  </FormLabel>
                  <p className="text-sm text-muted-foreground">
                    This event lasts all day
                  </p>
                </div>
              </FormItem>
            )}
          />

          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="startDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Start Date{!form.watch('allDay') && ' & Time'}</FormLabel>
                  <FormControl>
                    <Input 
                      type={form.watch('allDay') ? 'date' : 'datetime-local'}
                      value={
                        field.value instanceof Date 
                          ? form.watch('allDay') 
                            ? field.value.toISOString().split('T')[0]
                            : field.value.toISOString().slice(0, -1)
                          : ''
                      }
                      onChange={(e) => field.onChange(new Date(e.target.value))}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="endDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>End Date{!form.watch('allDay') && ' & Time'}</FormLabel>
                  <FormControl>
                    <Input 
                      type={form.watch('allDay') ? 'date' : 'datetime-local'}
                      value={
                        field.value instanceof Date 
                          ? form.watch('allDay') 
                            ? field.value.toISOString().split('T')[0]
                            : field.value.toISOString().slice(0, -1)
                          : ''
                      }
                      onChange={(e) => field.onChange(new Date(e.target.value))}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Location */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Location</h3>
          
          <FormField
            control={form.control}
            name="location"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Location (Optional)</FormLabel>
                <FormControl>
                  <Input placeholder="Office address, courthouse, virtual meeting link..." {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Reminder */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Reminder</h3>

          <FormField
            control={form.control}
            name="reminderMinutes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Reminder (Optional)</FormLabel>
                <Select
                  onValueChange={(value) => field.onChange(value ? parseInt(value) : undefined)}
                  defaultValue={field.value?.toString() || ''}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Set reminder time" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="">No reminder</SelectItem>
                    <SelectItem value="5">5 minutes before</SelectItem>
                    <SelectItem value="15">15 minutes before</SelectItem>
                    <SelectItem value="30">30 minutes before</SelectItem>
                    <SelectItem value="60">1 hour before</SelectItem>
                    <SelectItem value="120">2 hours before</SelectItem>
                    <SelectItem value="1440">1 day before</SelectItem>
                    <SelectItem value="2880">2 days before</SelectItem>
                    <SelectItem value="10080">1 week before</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Association */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Associate with</h3>
          
          <FormField
            control={form.control}
            name="matterId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Matter (Optional)</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a matter" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="">No specific matter</SelectItem>
                    {matters.map((matter) => {
                      const client = clients.find(c => c.id === matter.clientId)
                      return (
                        <SelectItem key={matter.id} value={matter.id}>
                          {matter.title}
                          {client && ` - ${client.firstName} ${client.lastName}`}
                        </SelectItem>
                      )
                    })}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {!selectedMatter && (
            <FormField
              control={form.control}
              name="clientId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Client (if no matter selected)</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a client" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="">No client</SelectItem>
                      {clients.map((client) => (
                        <SelectItem key={client.id} value={client.id}>
                          {client.firstName} {client.lastName}
                          {client.company && ` (${client.company})`}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          {selectedClient && (
            <div className="text-sm text-muted-foreground">
              Client: {selectedClient.firstName} {selectedClient.lastName}
              {selectedClient.company && ` (${selectedClient.company})`}
            </div>
          )}
        </div>

        {/* Form Actions */}
        <div className="flex justify-end gap-3 pt-4 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={() => {
              form.reset()
              onSuccess()
            }}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={form.formState.isSubmitting}>
            {form.formState.isSubmitting 
              ? 'Saving...' 
              : event 
                ? 'Update Event' 
                : 'Create Event'
            }
          </Button>
        </div>
      </form>
    </Form>
  )
} 