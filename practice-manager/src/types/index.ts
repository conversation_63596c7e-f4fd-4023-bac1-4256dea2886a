// Core entity types for Practice Manager

export interface Client {
  id: string
  firstName: string
  lastName: string
  email: string
  phone: string
  address: string
  city: string
  state: string
  zipCode: string
  company?: string
  notes?: string
  createdAt: Date
  updatedAt: Date
  status: 'active' | 'inactive' | 'prospective'
}

export interface Matter {
  id: string
  clientId: string
  title: string
  description: string
  matterType: 'litigation' | 'corporate' | 'real-estate' | 'criminal' | 'family' | 'immigration' | 'other'
  status: 'open' | 'closed' | 'pending' | 'on-hold'
  openDate: Date
  closeDate?: Date
  billableRate: number
  estimatedHours?: number
  actualHours: number
  totalBilled: number
  notes?: string
  createdAt: Date
  updatedAt: Date
}

export interface TimeEntry {
  id: string
  matterId: string
  clientId: string
  date: Date
  duration: number // in minutes
  description: string
  billableRate: number
  isBillable: boolean
  isInvoiced: boolean
  createdAt: Date
  updatedAt: Date
}

export interface Document {
  id: string
  clientId?: string
  matterId?: string
  name: string
  description?: string
  type: string
  size: number
  url: string
  uploadDate: Date
  category: 'contract' | 'correspondence' | 'court-filing' | 'evidence' | 'legal-brief' | 'memo' | 'other'
  tags: string[]
  createdAt: Date
  updatedAt: Date
}

export interface Event {
  id: string
  title: string
  description?: string
  startDate: Date
  endDate: Date
  type: 'appointment' | 'court-date' | 'deadline' | 'meeting' | 'reminder'
  clientId?: string
  matterId?: string
  location?: string
  allDay: boolean
  reminderMinutes?: number // Minutes before event to show reminder
  reminderSent?: boolean // Track if reminder was already shown
  createdAt: Date
  updatedAt: Date
}

export interface Invoice {
  id: string
  clientId: string
  matterId?: string
  invoiceNumber: string
  issueDate: Date
  dueDate: Date
  status: 'draft' | 'sent' | 'paid' | 'overdue'
  timeEntries: string[] // TimeEntry IDs
  subtotal: number
  tax: number
  total: number
  notes?: string
  createdAt: Date
  updatedAt: Date
}

// Form types for creating/editing entities
export interface ClientFormData {
  firstName: string
  lastName: string
  email: string
  phone: string
  address: string
  city: string
  state: string
  zipCode: string
  company?: string
  notes?: string
  status: 'active' | 'inactive' | 'prospective'
}

export interface MatterFormData {
  clientId: string
  title: string
  description: string
  matterType: 'litigation' | 'corporate' | 'real-estate' | 'criminal' | 'family' | 'immigration' | 'other'
  status: 'open' | 'closed' | 'pending' | 'on-hold'
  openDate: Date
  closeDate?: Date
  billableRate: number
  estimatedHours?: number
  notes?: string
}

export interface TimeEntryFormData {
  matterId: string
  date: Date
  duration: number
  description: string
  billableRate: number
  isBillable: boolean
}

export interface DocumentFormData {
  clientId?: string
  matterId?: string
  name: string
  description?: string
  type: string
  category: 'contract' | 'correspondence' | 'court-filing' | 'evidence' | 'legal-brief' | 'memo' | 'other'
  tags: string[]
}

export interface EventFormData {
  title: string
  description?: string
  startDate: Date
  endDate: Date
  type: 'appointment' | 'court-date' | 'deadline' | 'meeting' | 'reminder'
  clientId?: string
  matterId?: string
  location?: string
  allDay: boolean
  reminderMinutes?: number
}

// Dashboard statistics
export interface DashboardStats {
  totalClients: number
  activeMatters: number
  totalRevenue: number
  unbilledHours: number
  recentTimeEntries: TimeEntry[]
  upcomingEvents: Event[]
}

// Search and filter types
export interface SearchFilters {
  query?: string
  status?: string[]
  matterType?: string[]
  dateRange?: {
    start: Date
    end: Date
  }
}

// Navigation and UI types
export interface MenuItem {
  id: string
  label: string
  href: string
  icon: string
  children?: MenuItem[]
}

export interface TableColumn<T> {
  key: keyof T
  label: string
  sortable?: boolean
  render?: (value: any, item: T) => React.ReactNode
}

// App state types
export interface AppState {
  clients: Client[]
  matters: Matter[]
  timeEntries: TimeEntry[]
  documents: Document[]
  events: Event[]
  invoices: Invoice[]
  isLoading: boolean
  error: string | null
}

// Action types for state management
export type AppAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'ADD_CLIENT'; payload: Client }
  | { type: 'UPDATE_CLIENT'; payload: Client }
  | { type: 'DELETE_CLIENT'; payload: string }
  | { type: 'ADD_MATTER'; payload: Matter }
  | { type: 'UPDATE_MATTER'; payload: Matter }
  | { type: 'DELETE_MATTER'; payload: string }
  | { type: 'ADD_TIME_ENTRY'; payload: TimeEntry }
  | { type: 'UPDATE_TIME_ENTRY'; payload: TimeEntry }
  | { type: 'DELETE_TIME_ENTRY'; payload: string }
  | { type: 'ADD_DOCUMENT'; payload: Document }
  | { type: 'UPDATE_DOCUMENT'; payload: Document }
  | { type: 'DELETE_DOCUMENT'; payload: string }
  | { type: 'ADD_EVENT'; payload: Event }
  | { type: 'UPDATE_EVENT'; payload: Event }
  | { type: 'DELETE_EVENT'; payload: string }
  | { type: 'LOAD_DATA'; payload: Partial<AppState> } 