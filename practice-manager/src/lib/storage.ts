import { AppState, Client, Matter, TimeEntry, Document, Event, Invoice } from '@/types'

const STORAGE_KEYS = {
  CLIENTS: 'practice-manager-clients',
  MATTERS: 'practice-manager-matters',
  TIME_ENTRIES: 'practice-manager-time-entries',
  DOCUMENTS: 'practice-manager-documents',
  EVENTS: 'practice-manager-events',
  INVOICES: 'practice-manager-invoices',
  APP_STATE: 'practice-manager-app-state'
}

// Generic storage utilities
export const storage = {
  get: <T>(key: string): T | null => {
    try {
      if (typeof window === 'undefined') return null
      const item = localStorage.getItem(key)
      return item ? JSON.parse(item, (key, value) => {
        // Revive Date objects
        if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(value)) {
          return new Date(value)
        }
        return value
      }) : null
    } catch (error) {
      console.error(`<PERSON>rror getting ${key} from localStorage:`, error)
      return null
    }
  },

  set: <T>(key: string, value: T): void => {
    try {
      if (typeof window === 'undefined') return
      localStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.error(`Error setting ${key} in localStorage:`, error)
    }
  },

  remove: (key: string): void => {
    try {
      if (typeof window === 'undefined') return
      localStorage.removeItem(key)
    } catch (error) {
      console.error(`Error removing ${key} from localStorage:`, error)
    }
  },

  clear: (): void => {
    try {
      if (typeof window === 'undefined') return
      Object.values(STORAGE_KEYS).forEach(key => localStorage.removeItem(key))
    } catch (error) {
      console.error('Error clearing localStorage:', error)
    }
  }
}

// Entity-specific storage functions
export const clientStorage = {
  getAll: (): Client[] => storage.get<Client[]>(STORAGE_KEYS.CLIENTS) || [],
  
  save: (clients: Client[]): void => storage.set(STORAGE_KEYS.CLIENTS, clients),
  
  add: (client: Client): void => {
    const clients = clientStorage.getAll()
    clients.push(client)
    clientStorage.save(clients)
  },
  
  update: (id: string, updatedClient: Partial<Client>): void => {
    const clients = clientStorage.getAll()
    const index = clients.findIndex(c => c.id === id)
    if (index !== -1) {
      clients[index] = { ...clients[index], ...updatedClient, updatedAt: new Date() }
      clientStorage.save(clients)
    }
  },
  
  delete: (id: string): void => {
    const clients = clientStorage.getAll()
    const filtered = clients.filter(c => c.id !== id)
    clientStorage.save(filtered)
  },
  
  getById: (id: string): Client | null => {
    const clients = clientStorage.getAll()
    return clients.find(c => c.id === id) || null
  }
}

export const matterStorage = {
  getAll: (): Matter[] => storage.get<Matter[]>(STORAGE_KEYS.MATTERS) || [],
  
  save: (matters: Matter[]): void => storage.set(STORAGE_KEYS.MATTERS, matters),
  
  add: (matter: Matter): void => {
    const matters = matterStorage.getAll()
    matters.push(matter)
    matterStorage.save(matters)
  },
  
  update: (id: string, updatedMatter: Partial<Matter>): void => {
    const matters = matterStorage.getAll()
    const index = matters.findIndex(m => m.id === id)
    if (index !== -1) {
      matters[index] = { ...matters[index], ...updatedMatter, updatedAt: new Date() }
      matterStorage.save(matters)
    }
  },
  
  delete: (id: string): void => {
    const matters = matterStorage.getAll()
    const filtered = matters.filter(m => m.id !== id)
    matterStorage.save(filtered)
  },
  
  getById: (id: string): Matter | null => {
    const matters = matterStorage.getAll()
    return matters.find(m => m.id === id) || null
  },
  
  getByClientId: (clientId: string): Matter[] => {
    const matters = matterStorage.getAll()
    return matters.filter(m => m.clientId === clientId)
  }
}

export const timeEntryStorage = {
  getAll: (): TimeEntry[] => storage.get<TimeEntry[]>(STORAGE_KEYS.TIME_ENTRIES) || [],
  
  save: (timeEntries: TimeEntry[]): void => storage.set(STORAGE_KEYS.TIME_ENTRIES, timeEntries),
  
  add: (timeEntry: TimeEntry): void => {
    const timeEntries = timeEntryStorage.getAll()
    timeEntries.push(timeEntry)
    timeEntryStorage.save(timeEntries)
  },
  
  update: (id: string, updatedTimeEntry: Partial<TimeEntry>): void => {
    const timeEntries = timeEntryStorage.getAll()
    const index = timeEntries.findIndex(t => t.id === id)
    if (index !== -1) {
      timeEntries[index] = { ...timeEntries[index], ...updatedTimeEntry, updatedAt: new Date() }
      timeEntryStorage.save(timeEntries)
    }
  },
  
  delete: (id: string): void => {
    const timeEntries = timeEntryStorage.getAll()
    const filtered = timeEntries.filter(t => t.id !== id)
    timeEntryStorage.save(filtered)
  },
  
  getByMatterId: (matterId: string): TimeEntry[] => {
    const timeEntries = timeEntryStorage.getAll()
    return timeEntries.filter(t => t.matterId === matterId)
  },
  
  getByClientId: (clientId: string): TimeEntry[] => {
    const timeEntries = timeEntryStorage.getAll()
    return timeEntries.filter(t => t.clientId === clientId)
  }
}

export const documentStorage = {
  getAll: (): Document[] => storage.get<Document[]>(STORAGE_KEYS.DOCUMENTS) || [],
  
  save: (documents: Document[]): void => storage.set(STORAGE_KEYS.DOCUMENTS, documents),
  
  add: (document: Document): void => {
    const documents = documentStorage.getAll()
    documents.push(document)
    documentStorage.save(documents)
  },
  
  update: (id: string, updatedDocument: Partial<Document>): void => {
    const documents = documentStorage.getAll()
    const index = documents.findIndex(d => d.id === id)
    if (index !== -1) {
      documents[index] = { ...documents[index], ...updatedDocument, updatedAt: new Date() }
      documentStorage.save(documents)
    }
  },
  
  delete: (id: string): void => {
    const documents = documentStorage.getAll()
    const filtered = documents.filter(d => d.id !== id)
    documentStorage.save(filtered)
  },
  
  getByClientId: (clientId: string): Document[] => {
    const documents = documentStorage.getAll()
    return documents.filter(d => d.clientId === clientId)
  },
  
  getByMatterId: (matterId: string): Document[] => {
    const documents = documentStorage.getAll()
    return documents.filter(d => d.matterId === matterId)
  }
}

export const eventStorage = {
  getAll: (): Event[] => storage.get<Event[]>(STORAGE_KEYS.EVENTS) || [],
  
  save: (events: Event[]): void => storage.set(STORAGE_KEYS.EVENTS, events),
  
  add: (event: Event): void => {
    const events = eventStorage.getAll()
    events.push(event)
    eventStorage.save(events)
  },
  
  update: (id: string, updatedEvent: Partial<Event>): void => {
    const events = eventStorage.getAll()
    const index = events.findIndex(e => e.id === id)
    if (index !== -1) {
      events[index] = { ...events[index], ...updatedEvent, updatedAt: new Date() }
      eventStorage.save(events)
    }
  },
  
  delete: (id: string): void => {
    const events = eventStorage.getAll()
    const filtered = events.filter(e => e.id !== id)
    eventStorage.save(filtered)
  },
  
  getUpcoming: (limit: number = 10): Event[] => {
    const events = eventStorage.getAll()
    const now = new Date()
    return events
      .filter(e => e.startDate >= now)
      .sort((a, b) => a.startDate.getTime() - b.startDate.getTime())
      .slice(0, limit)
  }
}

export const invoiceStorage = {
  getAll: (): Invoice[] => storage.get<Invoice[]>(STORAGE_KEYS.INVOICES) || [],
  
  save: (invoices: Invoice[]): void => storage.set(STORAGE_KEYS.INVOICES, invoices),
  
  add: (invoice: Invoice): void => {
    const invoices = invoiceStorage.getAll()
    invoices.push(invoice)
    invoiceStorage.save(invoices)
  },
  
  update: (id: string, updatedInvoice: Partial<Invoice>): void => {
    const invoices = invoiceStorage.getAll()
    const index = invoices.findIndex(i => i.id === id)
    if (index !== -1) {
      invoices[index] = { ...invoices[index], ...updatedInvoice, updatedAt: new Date() }
      invoiceStorage.save(invoices)
    }
  },
  
  delete: (id: string): void => {
    const invoices = invoiceStorage.getAll()
    const filtered = invoices.filter(i => i.id !== id)
    invoiceStorage.save(filtered)
  }
}

// Initialize storage with sample data if empty
export const initializeStorage = (): void => {
  if (typeof window === 'undefined') return
  
  // Only initialize if no data exists
  if (clientStorage.getAll().length === 0) {
    // Add sample clients
    const sampleClients: Client[] = [
      {
        id: '1',
        firstName: 'John',
        lastName: 'Smith',
        email: '<EMAIL>',
        phone: '(*************',
        address: '123 Main St',
        city: 'New York',
        state: 'NY',
        zipCode: '10001',
        company: 'Smith Enterprises',
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: '2',
        firstName: 'Sarah',
        lastName: 'Johnson',
        email: '<EMAIL>',
        phone: '(*************',
        address: '456 Oak Ave',
        city: 'Los Angeles',
        state: 'CA',
        zipCode: '90210',
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]
    
    // Add sample matters
    const sampleMatters: Matter[] = [
      {
        id: '1',
        clientId: '1',
        title: 'Contract Negotiation',
        description: 'Review and negotiate service agreement',
        matterType: 'corporate',
        status: 'open',
        openDate: new Date(),
        billableRate: 250,
        estimatedHours: 20,
        actualHours: 0,
        totalBilled: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]
    
    clientStorage.save(sampleClients)
    matterStorage.save(sampleMatters)
  }
}

// Export all storage functions
export {
  STORAGE_KEYS
} 